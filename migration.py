#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
数据迁移脚本：从 MySQL 迁移数据到 KingBase

使用方法：
    python migration.py --source-table mysql_users --target-table kb_users --batch-size 100000
"""

import logging
import argparse
import sys
from typing import List, Dict, Any, Generator
import time
from decimal import Decimal
import json
from datetime import datetime

import pymysql
from pymysql.cursors import DictCursor
import psycopg2
from psycopg2.extras import execute_values
from tqdm import tqdm

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migration.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

class MySQLConfig:
    """MySQL 数据库配置"""
    # HOST = "localhost"
    # PORT = 3306
    # USER = "root"
    # PASSWORD = "root"
    # DATABASE = "o45"
    # CHARSET = "utf8mb4"
    HOST = "**********"
    PORT = 3306
    USER = "tradebot"
    PASSWORD = "Ywzh!23Pwd*90"
    DATABASE = "dbreport"
    CHARSET = "utf8mb4"

class KingBaseConfig:
    """KingBase 数据库配置"""
    HOST = "***********"
    PORT = 54321
    USER = "aigc"
    PASSWORD = "KcO8EeYg^ZTh"
    DATABASE = "trade_digitalor"
    
    @classmethod
    def get_dsn(cls) -> str:
        """获取数据库连接字符串"""
        return f"host={cls.HOST} port={cls.PORT} dbname={cls.DATABASE} user={cls.USER} password={cls.PASSWORD}"

class DataMigrator:
    """数据迁移器"""
    
    def __init__(self, source_table: str, target_table: str, batch_size: int = 100000):
        """
        初始化数据迁移器
        
        Args:
            source_table: MySQL 源表名
            target_table: KingBase 目标表名
            batch_size: 每批处理的记录数
        """
        self.source_table = source_table
        self.target_table = target_table
        self.batch_size = batch_size
        self.mysql_conn = None
        self.kingbase_conn = None
        self.processed_count = 0
        self.error_count = 0
        self.field_types = {}
        
    def connect_mysql(self) -> None:
        """连接 MySQL 数据库"""
        try:
            self.mysql_conn = pymysql.connect(
                host=MySQLConfig.HOST,
                port=MySQLConfig.PORT,
                user=MySQLConfig.USER,
                password=MySQLConfig.PASSWORD,
                database=MySQLConfig.DATABASE,
                charset=MySQLConfig.CHARSET,
                cursorclass=DictCursor
            )
            logging.info("MySQL 数据库连接成功")
        except Exception as e:
            logging.error(f"MySQL 数据库连接失败: {str(e)}")
            raise

    def connect_kingbase(self) -> None:
        """连接 KingBase 数据库"""
        try:
            self.kingbase_conn = psycopg2.connect(KingBaseConfig.get_dsn())
            logging.info("KingBase 数据库连接成功")
        except Exception as e:
            logging.error(f"KingBase 数据库连接失败: {str(e)}")
            raise
            
    def get_field_types(self) -> None:
        """获取源表的字段类型信息"""
        try:
            with self.mysql_conn.cursor() as cursor:
                cursor.execute(f"SHOW COLUMNS FROM {self.source_table}")
                fields = cursor.fetchall()
                for field in fields:
                    self.field_types[field['Field']] = field['Type']
                logging.info("字段类型信息：")
                for field, type_info in self.field_types.items():
                    logging.info(f"  {field}: {type_info}")
        except Exception as e:
            logging.error(f"获取字段类型信息失败: {str(e)}")
            raise

    def preprocess_value(self, value: Any, field_name: str) -> Any:
        """
        预处理字段值，确保类型正确
        
        Args:
            value: 字段值
            field_name: 字段名
        
        Returns:
            处理后的值
        """
        if value is None:
            return None
            
        field_type = self.field_types.get(field_name, '').lower()
        
        try:
            logging.debug(f"处理字段 {field_name}，类型 {field_type}，原始值 {value}，值类型 {type(value)}")
            
            # ENUM 类型处理
            if field_type.startswith('enum'):
                if value == '' or value is None:
                    return None
                # 从 enum('value1','value2') 格式中提取值列表
                enum_values = field_type[5:-1].replace("'", "").split(',')
                str_value = str(value).strip()
                if str_value in enum_values:
                    return str_value
                logging.warning(f"ENUM 值 {value} 不在允许范围内: {enum_values}")
                return None
                
            # 数字类型处理
            elif 'int' in field_type:
                # 确保空字符串或None转为0
                if value == '' or value is None:
                    return 0
                # 处理可能的字符串数字
                if isinstance(value, str):
                    value = value.strip()
                    if value.isdigit() or (value.startswith('-') and value[1:].isdigit()):
                        return int(value)
                    return 0
                return int(value)
                
            elif 'decimal' in field_type or 'numeric' in field_type:
                # 处理 Decimal 类型
                if isinstance(value, Decimal):
                    return float(value)
                # 确保空字符串或None转为0.0
                if value == '' or value is None:
                    return 0.0
                # 处理可能的字符串数字
                if isinstance(value, str):
                    value = value.strip()
                    try:
                        return float(Decimal(value))
                    except:
                        return 0.0
                return float(value)
                
            elif 'float' in field_type or 'double' in field_type:
                # 确保空字符串或None转为0.0
                if value == '' or value is None:
                    return 0.0
                # 处理可能的字符串数字
                if isinstance(value, str):
                    value = value.strip()
                    try:
                        return float(value)
                    except ValueError:
                        return 0.0
                return float(value)
                
            # 布尔类型处理
            elif field_type == 'tinyint(1)' or field_type == 'bool' or field_type == 'boolean':
                if isinstance(value, bool):
                    return value
                if isinstance(value, (int, float)):
                    return bool(value)
                if isinstance(value, str):
                    value = value.strip().lower()
                    return value in ('1', 'true', 't', 'yes', 'y', 'on')
                return False
                
            # 字符类型处理
            elif field_type.startswith('char'):
                if value == '' or value is None:
                    return None
                str_value = str(value).strip()
                return str_value[:1] if str_value else None
                
            elif field_type.startswith('varchar'):
                if value == '' or value is None:
                    return None
                str_value = str(value).strip()
                if not str_value:
                    return None
                try:
                    max_length = int(field_type.split('(')[1].split(')')[0])
                    return str_value[:max_length]
                except (IndexError, ValueError):
                    return str_value
                    
            elif field_type == 'text' or field_type == 'longtext' or field_type == 'mediumtext':
                if value == '' or value is None:
                    return None
                return str(value).strip()
                
            # 日期时间类型处理
            elif 'datetime' in field_type or 'timestamp' in field_type:
                if isinstance(value, str):
                    value = value.strip()
                    if value in ('0000-00-00 00:00:00', '', 'None', 'null'):
                        return None
                    try:
                        # 尝试解析日期时间字符串
                        dt = datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
                        return dt.strftime('%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        logging.warning(f"无法解析日期时间值: {value}")
                        return None
                return value
                
            elif 'date' in field_type:
                if isinstance(value, str):
                    value = value.strip()
                    if value in ('0000-00-00', '', 'None', 'null'):
                        return None
                    try:
                        # 尝试解析日期字符串
                        dt = datetime.strptime(value, '%Y-%m-%d')
                        return dt.strftime('%Y-%m-%d')
                    except ValueError:
                        logging.warning(f"无法解析日期值: {value}")
                        return None
                return value
                
            # JSON 类型处理
            elif field_type == 'json':
                if value == '' or value is None:
                    return '{}'
                if isinstance(value, (dict, list)):
                    return json.dumps(value, ensure_ascii=False)
                if isinstance(value, str):
                    try:
                        # 验证是否为有效的 JSON
                        json.loads(value)
                        return value
                    except json.JSONDecodeError:
                        logging.warning(f"无效的 JSON 字符串: {value}")
                        return '{}'
                return '{}'
                
            # 其他类型，转为字符串
            else:
                if value == '' or value is None:
                    return None
                return str(value).strip()
                
        except (ValueError, TypeError) as e:
            logging.warning(f"字段 {field_name} 的值 {value} (类型 {type(value)}) 转换失败: {str(e)}")
            # 根据字段类型返回默认值
            if 'int' in field_type:
                return 0
            elif 'decimal' in field_type or 'numeric' in field_type or 'float' in field_type or 'double' in field_type:
                return 0.0
            elif field_type.startswith(('char', 'varchar', 'text')):
                return None
            elif field_type == 'json':
                return '{}'
            return None

    def preprocess_record(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """
        预处理记录中的所有字段
        
        Args:
            record: 原始记录
        
        Returns:
            处理后的记录
        """
        return {
            field: self.preprocess_value(value, field)
            for field, value in record.items()
        }
            
    def get_total_records(self) -> int:
        """获取需要迁移的总记录数"""
        with self.mysql_conn.cursor() as cursor:
            cursor.execute(f"SELECT COUNT(*) as count FROM {self.source_table}")
            result = cursor.fetchone()
            return result['count']
            
    def fetch_data(self) -> Generator[List[Dict[str, Any]], None, None]:
        """
        分批获取 MySQL 数据
        
        Yields:
            List[Dict[str, Any]]: 每批数据记录
        """
        with self.mysql_conn.cursor() as cursor:
            offset = 0
            while True:
                cursor.execute(
                    f"SELECT * FROM {self.source_table} LIMIT %s OFFSET %s",
                    (self.batch_size, offset)
                )
                batch = cursor.fetchall()
                if not batch:
                    break
                yield batch
                offset += self.batch_size

    def get_primary_key_columns(self) -> List[str]:
        """获取目标表的主键列名"""
        try:
            with self.kingbase_conn.cursor() as cursor:
                cursor.execute("""
                    SELECT a.attname
                    FROM pg_index i
                    JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
                    WHERE i.indrelid = %s::regclass AND i.indisprimary
                    ORDER BY a.attnum
                """, (self.target_table,))
                result = cursor.fetchall()
                return [row[0] for row in result]
        except Exception as e:
            logging.error(f"获取主键信息失败: {str(e)}")
            # 如果获取失败，尝试从建表语句中推断
            return ['fund_id']  # 根据您的建表语句，默认使用fund_id作为主键

    def filter_existing_records(self, batch: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤已存在的记录"""
        if not batch:
            return []
            
        primary_keys = self.get_primary_key_columns()
        if not primary_keys:
            logging.warning("未找到主键信息，跳过重复检查")
            return batch
            
        try:
            # 构建查询条件
            pk_values = []
            for record in batch:
                pk_tuple = tuple(record.get(pk) for pk in primary_keys)
                pk_values.append(pk_tuple)
            
            # 查询已存在的记录
            if len(primary_keys) == 1:
                # 单主键情况
                pk_name = primary_keys[0]
                placeholders = ','.join(['%s'] * len(pk_values))
                query = f"SELECT {pk_name} FROM {self.target_table} WHERE {pk_name} IN ({placeholders})"
                flat_values = [pk_tuple[0] for pk_tuple in pk_values]
                
                with self.kingbase_conn.cursor() as cursor:
                    cursor.execute(query, flat_values)
                    existing_pks = {row[0] for row in cursor.fetchall()}
                
                # 过滤掉已存在的记录
                filtered_batch = []
                for record in batch:
                    if record.get(pk_name) not in existing_pks:
                        filtered_batch.append(record)
                        
            else:
                # 复合主键情况
                existing_pks = set()
                pk_conditions = []
                query_values = []
                
                for pk_tuple in pk_values:
                    condition_parts = []
                    for i, pk_name in enumerate(primary_keys):
                        condition_parts.append(f"{pk_name} = %s")
                        query_values.append(pk_tuple[i])
                    pk_conditions.append(f"({' AND '.join(condition_parts)})")
                
                if pk_conditions:
                    query = f"SELECT {','.join(primary_keys)} FROM {self.target_table} WHERE {' OR '.join(pk_conditions)}"
                    
                    with self.kingbase_conn.cursor() as cursor:
                        cursor.execute(query, query_values)
                        existing_pks = {tuple(row) for row in cursor.fetchall()}
                
                # 过滤掉已存在的记录
                filtered_batch = []
                for record in batch:
                    record_pk = tuple(record.get(pk) for pk in primary_keys)
                    if record_pk not in existing_pks:
                        filtered_batch.append(record)
            
            skipped_count = len(batch) - len(filtered_batch)
            if skipped_count > 0:
                logging.info(f"跳过 {skipped_count} 条已存在的记录")
                
            return filtered_batch
            
        except Exception as e:
            logging.error(f"过滤已存在记录时出错: {str(e)}")
            # 出错时返回原始批次，让后续处理决定如何处理
            return batch

    def migrate_batch(self, batch: List[Dict[str, Any]]) -> None:
        """
        迁移一批数据到 KingBase
        
        Args:
            batch: 要迁移的数据批次
        """
        if not batch:
            return
            
        try:
            # 打印第一条记录的样本数据
            # if self.processed_count == 0:
            #     sample_record = batch[0]
                # logging.info("样本数据:")
                # for key, value in sample_record.items():
                #     logging.info(f"  {key}: {value} (类型: {type(value)})")
            
            # 预处理每条记录
            processed_batch = []
            error_records = []
            
            for idx, record in enumerate(batch):
                try:
                    processed_record = self.preprocess_record(record)
                    processed_batch.append(processed_record)
                except Exception as e:
                    error_records.append({
                        'index': idx,
                        'record': record,
                        'error': str(e)
                    })
                    logging.error(f"记录处理失败: {str(e)}")
                    logging.error(f"问题记录: {record}")
                    continue
            
            if error_records:
                logging.warning(f"本批次有 {len(error_records)} 条记录处理失败")
            
            if not processed_batch:
                logging.error("没有可处理的记录")
                return
            
            # 过滤已存在的记录
            filtered_batch = self.filter_existing_records(processed_batch)
            
            if not filtered_batch:
                logging.info("本批次所有记录都已存在，跳过插入")
                return
            
            # 准备列名和值
            columns = list(filtered_batch[0].keys())
            values = [[record[column] for column in columns] for record in filtered_batch]
            
            # 打印 SQL 模板（用于调试）
            if self.processed_count == 0:
                logging.info("SQL模板:")
                logging.info(f"INSERT INTO {self.target_table} ({', '.join(columns)}) VALUES %s")
                logging.info("第一条记录的值:")
                logging.info(values[0] if values else "无数据")
            
            # 使用 psycopg2 的 execute_values 执行批量插入
            try:
                with self.kingbase_conn.cursor() as cursor:
                    # 记录开始时间
                    start_time = time.time()
                    
                    # 执行批量插入
                    execute_values(cursor, f"INSERT INTO {self.target_table} ({', '.join(columns)}) VALUES %s", values)
                    
                    # 提交事务
                    self.kingbase_conn.commit()
                    
                    # 计算执行时间
                    end_time = time.time()
                    execution_time = end_time - start_time
                    
                    # 更新计数器
                    successful_inserts = len(values)
                    self.processed_count += successful_inserts
                    
                    # 记录性能指标
                    records_per_second = successful_inserts / execution_time if execution_time > 0 else 0
                    logging.info(f"本批次处理完成:")
                    logging.info(f"  - 成功插入: {successful_inserts} 条记录")
                    logging.info(f"  - 跳过重复: {len(processed_batch) - len(filtered_batch)} 条记录")
                    logging.info(f"  - 执行时间: {execution_time:.2f} 秒")
                    logging.info(f"  - 处理速度: {records_per_second:.2f} 记录/秒")
                    if error_records:
                        logging.info(f"  - 失败记录: {len(error_records)} 条")
            
            except Exception as e:
                self.error_count += 1
                self.kingbase_conn.rollback()
                logging.error(f"批次处理失败: {str(e)}")
                logging.error("尝试逐条插入...")
                
                # 如果批量插入失败，尝试逐条插入
                successful_single_inserts = 0
                single_insert_errors = []
                
                for idx, record in enumerate(processed_batch):
                    try:
                        columns = list(record.keys())
                        placeholders = ', '.join(['%s'] * len(columns))
                        values = [record[column] for column in columns]
                        
                        with self.kingbase_conn.cursor() as cursor:
                            cursor.execute(
                                f"INSERT INTO {self.target_table} ({', '.join(columns)}) VALUES ({placeholders})",
                                values
                            )
                            self.kingbase_conn.commit()
                            successful_single_inserts += 1
                            
                    except Exception as single_error:
                        single_insert_errors.append({
                            'index': idx,
                            'record': record,
                            'error': str(single_error)
                        })
                        self.kingbase_conn.rollback()
                        continue
                
                if successful_single_inserts > 0:
                    logging.info(f"逐条插入成功: {successful_single_inserts} 条记录")
                    self.processed_count += successful_single_inserts
                
                if single_insert_errors:
                    logging.error(f"逐条插入失败: {len(single_insert_errors)} 条记录")
                    for error in single_insert_errors:
                        logging.error(f"  索引 {error['index']}: {error['error']}")
                
                if not successful_single_inserts:
                    raise Exception("批量插入和逐条插入都失败")
            
        except Exception as e:
            self.error_count += 1
            self.kingbase_conn.rollback()
            logging.error(f"批次处理完全失败: {str(e)}")
            raise

    def verify_migration(self, batch_size: int = 1000) -> None:
        """
        验证迁移的数据是否正确
        
        Args:
            batch_size: 每批验证的记录数
        """
        # 重新连接数据库进行验证
        mysql_conn = None
        kingbase_conn = None
        
        try:
            # 重新建立连接
            mysql_conn = pymysql.connect(
                host=MySQLConfig.HOST,
                port=MySQLConfig.PORT,
                user=MySQLConfig.USER,
                password=MySQLConfig.PASSWORD,
                database=MySQLConfig.DATABASE,
                charset=MySQLConfig.CHARSET,
                cursorclass=DictCursor
            )
            
            kingbase_conn = psycopg2.connect(KingBaseConfig.get_dsn())
            
            # 获取源表和目标表的记录数
            with mysql_conn.cursor() as cursor:
                cursor.execute(f"SELECT COUNT(*) as count FROM {self.source_table}")
                mysql_count = cursor.fetchone()['count']
                
            with kingbase_conn.cursor() as cursor:
                cursor.execute(f"SELECT COUNT(*) as count FROM {self.target_table}")
                kingbase_result = cursor.fetchone()
                kingbase_count = kingbase_result[0]  # PostgreSQL 返回元组，取第一个元素
                
            logging.info("数据验证:")
            logging.info(f"MySQL 记录数: {mysql_count}")
            logging.info(f"KingBase 记录数: {kingbase_count}")
            
            if mysql_count != kingbase_count:
                logging.warning(f"记录数不匹配: MySQL {mysql_count} vs KingBase {kingbase_count}")
            else:
                logging.info("记录数验证通过！")
            
            # 如果记录数相同，进行抽样验证
            if mysql_count == kingbase_count and mysql_count > 0:
                sample_size = min(100, mysql_count)  # 抽样验证100条记录
                logging.info(f"开始抽样验证 {sample_size} 条记录...")
                
                # 随机抽取记录进行验证
                with mysql_conn.cursor() as cursor:
                    cursor.execute(f"SELECT * FROM {self.source_table} ORDER BY RAND() LIMIT {sample_size}")
                    sample_records = cursor.fetchall()
                
                mismatches = 0
                for mysql_record in sample_records:
                    # 构建 WHERE 子句
                    where_conditions = []
                    where_values = []
                    
                    for key, value in mysql_record.items():
                        if value is None:
                            where_conditions.append(f"{key} IS NULL")
                        else:
                            where_conditions.append(f"{key} = %s")
                            where_values.append(self.preprocess_value(value, key))
                    
                    where_clause = " AND ".join(where_conditions)
                    
                    # 在 KingBase 中查找记录
                    with kingbase_conn.cursor() as cursor:
                        query = f"SELECT COUNT(*) FROM {self.target_table} WHERE {where_clause}"
                        cursor.execute(query, where_values)
                        kingbase_match_result = cursor.fetchone()
                        kingbase_match_count = kingbase_match_result[0]  # PostgreSQL 返回元组
                        
                        if kingbase_match_count == 0:
                            mismatches += 1
                
                if mismatches == 0:
                    logging.info(f"抽样验证通过！{sample_size} 条记录全部匹配")
                else:
                    logging.warning(f"抽样验证发现 {mismatches} 条不匹配记录")
                    
        except Exception as e:
            logging.error(f"数据验证失败: {str(e)}")
            raise
        finally:
            # 关闭验证用的连接
            if mysql_conn:
                try:
                    mysql_conn.close()
                except:
                    pass
            if kingbase_conn:
                try:
                    kingbase_conn.close()
                except:
                    pass

    def migrate(self) -> None:
        """执行数据迁移"""
        try:
            self.connect_mysql()
            self.connect_kingbase()
            
            # 获取字段类型信息
            self.get_field_types()
            
            # 获取主键信息
            primary_keys = self.get_primary_key_columns()
            logging.info(f"目标表主键: {primary_keys}")
            
            total_records = self.get_total_records()
            
            logging.info(f"开始迁移数据:")
            logging.info(f"源表: {self.source_table} (MySQL)")
            logging.info(f"目标表: {self.target_table} (KingBase)")
            logging.info(f"总记录数: {total_records}")
            logging.info(f"启用重复检查: 是")
            
            skipped_total = 0
            
            with tqdm(total=total_records, desc="迁移进度") as pbar:
                for batch in self.fetch_data():
                    old_count = self.processed_count
                    batch_size = len(batch)
                    
                    self.migrate_batch(batch)
                    
                    # 计算跳过的记录数
                    actual_processed = self.processed_count - old_count
                    skipped_in_batch = batch_size - actual_processed
                    skipped_total += skipped_in_batch
                    
                    # 更新进度条（包括跳过的记录）
                    pbar.update(batch_size)
                    
            logging.info(f"迁移完成。成功处理 {self.processed_count} 条记录，"
                        f"跳过重复 {skipped_total} 条记录，"
                        f"失败 {self.error_count} 次")
            
            # 验证迁移的数据 - 重新连接数据库
            if self.processed_count > 0:
                logging.info("开始验证数据...")
                self.verify_migration()
            else:
                logging.info("没有新数据插入，跳过数据验证")
            
        except Exception as e:
            logging.error(f"迁移过程中发生错误: {str(e)}")
            raise
        finally:
            # 确保连接被正确关闭
            if self.mysql_conn:
                try:
                    self.mysql_conn.close()
                except:
                    pass
            if self.kingbase_conn:
                try:
                    self.kingbase_conn.close()
                except:
                    pass

    def test_primary_key_detection(self) -> None:
        """测试主键检测功能"""
        try:
            self.connect_kingbase()
            primary_keys = self.get_primary_key_columns()
            
            if primary_keys:
                logging.info(f"检测到主键: {primary_keys}")
                
                # 测试查询一条记录验证主键
                with self.kingbase_conn.cursor() as cursor:
                    cursor.execute(f"SELECT {', '.join(primary_keys)} FROM {self.target_table} LIMIT 1")
                    result = cursor.fetchone()
                    if result:
                        logging.info(f"主键测试成功，样本主键值: {result}")
                    else:
                        logging.info("目标表为空，无法测试主键值")
            else:
                logging.error("未能检测到主键")
                
        except Exception as e:
            logging.error(f"主键检测测试失败: {str(e)}")
        finally:
            if self.kingbase_conn:
                self.kingbase_conn.close()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="数据迁移工具")
    parser.add_argument('--source-table', required=True, help='MySQL 源表名')
    parser.add_argument('--target-table', help='KingBase 目标表名（如果不指定，将使用与源表相同的名称）')
    parser.add_argument('--batch-size', type=int, default=100000, help='每批处理的记录数')
    parser.add_argument('--test-pk', action='store_true', help='仅测试主键检测功能')
    
    args = parser.parse_args()
    
    # 如果未指定目标表名，使用源表名
    target_table = args.target_table if args.target_table else args.source_table
    
    try:
        migrator = DataMigrator(args.source_table, target_table, args.batch_size)
        
        if args.test_pk:
            # 仅测试主键检测
            migrator.test_primary_key_detection()
        else:
            # 执行完整迁移
            migrator.migrate()
            
    except Exception as e:
        logging.error(f"操作失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
