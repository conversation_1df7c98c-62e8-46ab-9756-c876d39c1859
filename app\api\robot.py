#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2019 Tianxiadatong, Co.,Ltd. All Rights Reserved.

This module implements the FastAPI sub-router for the /robot endpoint,
including related business logic.

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06
"""

import io
import logging
import random
from datetime import datetime
from typing import List

import pandas as pd
from fastapi import APIRouter, Depends, Request, HTTPException, UploadFile, File
from fastapi.encoders import jsonable_encoder
from sqlalchemy import select, exists
from sqlalchemy.orm import Session

from app.assets.public_assets import general_return_json
from app.core.database import get_db
from app.decorator.auth import required_login
from app.external_service import qq_websocket_channel, wechat_websocket_channel
from app.models.models import Robot, User, Log
from app.schemas.schemas import RobotModify, RobotCreate

router = APIRouter(prefix="/robot")

@router.post("/import", summary="开导csv")
@required_login(roles=["admin", "super_admin"])
async def import_robot(request: Request, db: Session=Depends(get_db), file: UploadFile=File(...)):
    """导入机器人数据（CSV文件）。

    Args:
        request (Request): FastAPI 请求对象
        db (Session): 数据库会话对象
        file (UploadFile): 上传的CSV文件，必须是GBK编码的CSV格式

    Returns:
        general_return_json: 返回结果
            - 200: 导入成功
            - 400: 文件格式错误

    Raises:
        HTTPException: 文件读取或数据库操作异常
    """

    user = db.get(User, request.session.get("auth").get("id"))
    log = Log(
        operator=user.nickname,
        target=str(file.filename),
        action="导入机器人操作",
        object="机器人",
        result=False,
    )

    if not file.filename.endswith('.csv'):

        log.result = False
        log.clause = "仅支持 CSV 文件"
        db.add(log)

        db.commit()
        return general_return_json(400, "仅支持 CSV 文件")

    try:


        content = await file.read()
        df = pd.read_csv(io.StringIO(content.decode('gbk')),dtype=str)
        rows: List[List] = df.values.tolist()

        for row in rows:
            robot = RobotCreate(
                type=row[0],
                account=row[1],
                gender=row[2],
                tel=row[3],
                email=row[4],
                status=False,
                enable=True,
                uni=random.sample(range(0, 2_147_483_646), 1)[0],
                nickname=row[5]
            )
            stmt = select(exists().where(Robot.account == robot.account))
            if not db.scalar(stmt):
             db.add(robot)
        db.commit()
        logging.info(f"从文件 {file.filename} 导入了 {len(rows)} 个机器人")
        log.target += f"，导入了{len(rows)} 个机器人"
        log.result = True
        db.add(log)
        db.commit()
        return general_return_json(200, "ok")
    except Exception as e:
        db.rollback()
        logging.error(f"导入文件出错: {str(e)}")
        log.result = False
        log.cause = str(e)
        db.add(log)
        db.commit()

        raise HTTPException(status_code=500, detail=f"读取失败，请检查文件内容: {str(e)}")

@router.post("/create", summary="新建个G7人")
@required_login(roles=["admin", "super_admin"])
async def create_robot(robot_parameter: RobotCreate, request: Request, db: Session=Depends(get_db)):
    """创建新的机器人。

    Args:
        robot_parameter (RobotCreate): 机器人创建参数
        request (Request): FastAPI 请求对象
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 创建成功
            - 400: 机器人已存在

    Raises:
        HTTPException: 数据库操作异常
    """

    def generate_number_string(pk: int) -> str:
        # 获取当前日期：YYYYMMDD 格式
        date_str = datetime.now().strftime("%Y%m%d")
        # 编号部分：PK 转为 3 位，不足补零
        pk_str = f"{pk:03d}"
        # 拼接生成结果
        return date_str + pk_str

    user = db.get(User, request.session.get("auth").get("id"))
    log = Log(
        operator=user.nickname,
        target=str(robot_parameter.dict()),
        action="新增机器人操作",
        object="机器人",
        result=False,
    )

    try:
        robot_existance = db.query(Robot).filter(
            Robot.type == robot_parameter.type,
            Robot.account == robot_parameter.account,
        ).first()

        if robot_existance:
            return general_return_json(400, "该机器人已存在")

        new_robot_data = robot_parameter.dict()
        new_robot_data["uni"] = random.sample(range(0, 2_147_483_646), 1)[0]

        new_robot = Robot(**new_robot_data)


        db.add(new_robot)
        db.flush()
        db.commit()

        log.result = True
        log.target += f",创建了新机器人 {robot_parameter.nickname}"
        db.add(log)
        db.commit()

        logging.info(f"创建了新机器人 {robot_parameter.nickname}")
        return general_return_json(200, "ok")

    except Exception as e:
        db.rollback()
        logging.error(f"创建新机器人出错: {str(e)}")
        log.result = False
        db.add(log)
        db.commit()
        raise HTTPException(status_code=500, detail=f"创建出错: {str(e)}")

@router.post("/modify", summary=" 改机器人info")
@required_login(roles=["admin", "super_admin"])
async def modify_robot(robot_parameter: RobotModify, request: Request, db: Session=Depends(get_db)):
    """修改机器人信息。

    Args:
        robot_parameter (RobotModify): 机器人修改参数
        request (Request): FastAPI 请求对象
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 修改成功或无需修改
            - 404: 机器人不存在

    Raises:
        HTTPException: 数据库操作异常
    """
    user = db.get(User, request.session.get("auth").get("id"))
    log = Log(
        operator=user.nickname,
        target=str(robot_parameter.dict()),
        action="修改机器人操作",
        object="机器人",
        result=False,
    )

    try:
        robot = db.get(Robot, robot_parameter.id)
        if not robot:
            return general_return_json(404, "没这机器人")

        updated = False

        if robot_parameter.enable is not None:
            robot.enable = robot_parameter.enable
            updated = True
        if robot_parameter.tel is not None:
            robot.tel = robot_parameter.tel
            updated = True
        if robot_parameter.email is not None:
            robot.email = robot_parameter.email
            updated = True
        if robot_parameter.nickname is not None:
            robot.nickname = robot_parameter.nickname
            updated = True

        if updated:
            log.result = True
            log.target += f",修改了机器人 ID - {robot.id} 信息"
            db.add(log)

            db.commit()
            logging.info(f"修改了机器人 ID - {robot.id} 信息")
            return general_return_json(200, "修改成功")
        else:
            db.rollback()

            log.result = True
            log.target += f",但是没有任何不相同的可修改字段"
            db.add(log)

            return general_return_json(200, "没有任何可修改字段")

    except Exception as e:
        db.rollback()
        log.result = False
        log.cause = str(e)
        db.add(log)

        db.commit()
        logging.error(f"修改机器人出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"修改出错: {str(e)}")

@router.get("/", summary="get所有机器人，一共就8个就不分页了吧")
@required_login(roles=["admin","salesman", "super_admin"])
async def get_all_robot(request: Request, db: Session=Depends(get_db)):
    """获取所有机器人列表。

    由于机器人数量较少（约8个），不进行分页处理。

    Args:
        request (Request): FastAPI 请求对象
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 返回机器人列表

    Raises:
        HTTPException: 数据库操作异常
    """
    try:
        await update_robot_status()
        robots = db.query(Robot).order_by(Robot.id).all()
        return general_return_json(200, "ok", robots)
    except Exception as e:

        logging.error(f"获取全部机器人出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取出错: {str(e)}")

async def update_robot_status():
    db_gen = get_db()
    db: Session = next(db_gen)
    try:
        robots = db.query(Robot).order_by(Robot.id).all()
        return general_return_json(200, "ok", robots)
    except Exception as e:
        db.rollback()
        logging.error(f"更新全部机器人状态出错: {str(e)}")