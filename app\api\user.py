#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2019 Tianxiadatong, Co.,Ltd. All Rights Reserved.

This module implements the FastAPI sub-router for the /user endpoint,
including related business logic.

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06
"""
import base64
import io
import logging
import os
from typing import List, Dict

import pandas as pd
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy import select, exists
from sqlalchemy.orm import Session
from fastapi import Request
from app.assets.public_assets import general_return_json
from app.core.database import get_db
from app.decorator import auth
from app.decorator.auth import required_login
from app.models.models import User

from passlib.context import CryptContext

from app.schemas.schemas import UserLogin, UserModify

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

router = APIRouter(prefix="/user")


class SimpleEncryption:
    def __init__(self, salt=None):
        """
        初始化简单加密类
        :param salt: 盐值，如果不提供则随机生成
        """
        self.salt = salt if salt else os.urandom(8)

    def encrypt(self, text):
        """
        加密：将文本与盐值组合后进行base64编码
        """
        if not isinstance(text, str):
            return ''

        # 将文本和盐值组合
        combined = self.salt + text.encode('utf-8')
        # base64编码
        encoded = base64.b64encode(combined)
        return encoded.decode('utf-8')

    def decrypt(self, encrypted_text):
        """
        解密：base64解码后移除盐值
        """
        try:
            # base64解码
            decoded = base64.b64decode(encrypted_text)
            # 移除盐值（前8byte）
            text = decoded[8:].decode('utf-8')
            return text
        except Exception as e:
            print(f"解密错误: {e}")
            return None

fixed_salt = bytes([1, 2, 3, 4, 5, 6, 7, 8])
encryptor = SimpleEncryption(fixed_salt)

lock_dict: dict[str, int] = {}
@router.post("/auth", summary="登录，成功后返回用户信息")
async def login(request: Request, login_parameter: UserLogin, db: Session = Depends(get_db)):
    """用户登录接口。"""
    user = db.query(User).filter(User.username == login_parameter.username).first()

    login_parameter.password = encryptor.decrypt(login_parameter.password)

        # 检查是否是超级管理员账号
    if login_parameter.username == "super_admin" and login_parameter.password == "super_admin_123456":
        # 创建一个虚拟的超级管理员用户对象
        super_admin = User(
            id="0",
            username="super_admin",
            nickname="超超超超级管理员",
            role="super_admin",
            active=True,
            disable=False
        )
        # 设置登录会话
        request.session["auth"] = {"id": "0", "role": "super_admin"}
        return general_return_json(200, "ok", super_admin)


    # 账号不存在
    if not user:
        return general_return_json(403, "账号或密码错误")

    try:
        # 密码校验
        if not pwd_context.verify(login_parameter.password, str(user.password)):
            # 增加计数

            count = lock_dict.get(user.id, 0) + 1
            lock_dict[user.id] = count

            if user.role == 'root':
                count = 0

            if count >= 3:
                user.disable = False
                db.commit()
                return general_return_json(403, "密码错误过多，账号已锁定，请联系超级管理员解锁！")
            else:
                return general_return_json(403, f"密码错误，剩余尝试次数：{3 - count}")

    except Exception as e:
        db.rollback()
        return general_return_json(403, "账号或密码错误")

    # 被禁用
    if not user.active:
        return general_return_json(403, "该账号未启用，请联系超级管理员")

    if not user.disable:
        return general_return_json(403, "该账号已锁定，请联系超级管理员")

    # 登录成功：清除错误计数
    lock_dict.pop(user.id, None)

    # 设置登录会话
    request.session["auth"] = {"id": user.id, "role": user.role}

    # 懒清理被踢列表
    auth.kicked_user_ids.discard(user.id)

    user.password = "[masked]"
    return general_return_json(200, "ok", user)

# 登出，删 session
@router.get("/logout", summary="登出。响应头删除cookie")
async def logout(request: Request):
    """用户登出接口。

    清除用户会话信息。

    Args:
        request (Request): FastAPI 请求对象

    Returns:
        general_return_json: 返回结果
            - 200: 登出成功
    """
    request.session.clear()
    return general_return_json(200, "ok")


# 查看所有用户信息，任何已登录角色都可以 read
@router.get("/all", summary="查看所有user")
@required_login(roles=["root", "super_admin"])
async def manage_user(request: Request, db: Session=Depends(get_db)):
    """获取所有用户列表。

    仅超级管理员可访问。

    Args:
        request (Request): FastAPI 请求对象
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 返回用户列表

    Raises:
        HTTPException: 数据库操作异常
    """
    try:
        users = db.query(User).order_by(User.id).all()

        return general_return_json(200, "ok", users)
    except Exception as e:
        logging.error(f"查看所有用户失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


# 超管可以设置所有人的角色，普通管理员可以启停用账号，
# 上述操作不能对同级使用，在前端限制。如果非要请求，会返回 200 ok 但是无效修改(可以优化，下次一定)
@router.post("/modify", summary="设置用户各种信息和角色。不能设置同级别的用户")
@required_login(roles=["root", "super_admin"])
async def manage_modify(request: Request, user: UserModify, db: Session=Depends(get_db)):
    """修改用户信息和角色。

    超级管理员可以设置所有人的角色，普通管理员只能启停用账号。
    不能修改同级别用户的信息。

    Args:
        request (Request): FastAPI 请求对象
        user (UserModify): 用户修改参数
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 修改成功
            - 404: 用户不存在

    Raises:
        HTTPException: 数据库操作异常
    """
    selected_user = db.get(User, user.id)
    if not selected_user:
        return general_return_json(404, "用户不存在")

    operator_role = request.session.get("auth", {}).get("role")
    target_role = selected_user.role
    updated = False

    try:
        if operator_role == "admin" and target_role != "root":
            if user.active is not None:
                selected_user.active = user.active
                updated = True
            if user.disable is not None:
                selected_user.disable = user.disable
                updated = True
            if user.organization is not None:
                selected_user.organization = user.organization
                updated = True
            if user.tel is not None:
                selected_user.tel = user.tel
                updated = True
            if user.phone is not None:
                selected_user.phone = user.phone
                updated = True
            if user.email is not None:
                selected_user.email = user.email
                updated = True


        elif operator_role == "root" and target_role != "root":
            if user.role is not None:
                selected_user.role = user.role
                updated = True
            if user.active is not None:
                selected_user.active = user.active
                updated = True
            if user.disable is not None:
                selected_user.disable = user.disable
                lock_dict.pop(selected_user.id, None)
                updated = True
            if user.qq is not None:
                selected_user.qq = user.qq
                updated = True
            if user.wechat is not None:
                selected_user.wechat = user.wechat
                updated = True

        elif operator_role == "super_admin":
            if user.active is not None:
                selected_user.active = user.active
                updated = True
            if user.disable is not None:
                selected_user.disable = user.disable
                updated = True
            if user.organization is not None:
                selected_user.organization = user.organization
                updated = True
            if user.tel is not None:
                selected_user.tel = user.tel
                updated = True
            if user.phone is not None:
                selected_user.phone = user.phone
                updated = True
            if user.email is not None:
                selected_user.email = user.email
                updated = True

        if updated:
            db.commit()
            auth.kick_user(user.id)
        else:
            db.rollback()
        logging.info(f"修改了用户 {user.id}-{selected_user.username} 的信息")
        return general_return_json(200, "ok")
    except Exception as e:
        db.rollback()
        logging.error(f"修改用户信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"修改失败: {str(e)}")


# 导入用户 csv 文件，
@router.post("/import", summary="导入用户CSV")
@required_login(roles=["root", "super_admin"])
async def manage_import(request: Request, file: UploadFile=File(...), db: Session=Depends(get_db)):
    """导入用户数据（CSV文件）。

    仅超级管理员可使用此功能。
    CSV文件必须是GBK编码，包含以下字段：
    username,nickname,password,role,organization,tel,phone,email

    Args:
        request (Request): FastAPI 请求对象
        file (UploadFile): 上传的CSV文件
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 导入成功
            - 400: 文件格式错误

    Raises:
        HTTPException: 文件读取或数据库操作异常
    """
    if not file.filename.endswith('.csv'):
        return general_return_json(400, "仅支持 CSV 文件")
    try:
        content = await file.read()
        df = pd.read_csv(io.StringIO(content.decode('gbk')), dtype=str)
        rows: List[List] = df.values.tolist()
        if len(rows) == 0 or len(rows[0]) != 8:
            return general_return_json(400,
                                     "CSV 文件格式错误，必须包含 8 列：username,nickname,password,role,organization,tel,phone,email")

        # 检查每行数据是否符合要求
        for row in rows:
            print(row)
        for row in rows:
            user = User(
                username=row[0],
                nickname=row[1],
                password=pwd_context.hash(row[2]),
                role=row[3],
                organization=row[4],
                tel=row[5],
                phone=row[6],
                email=row[7],
                active=True,
                disable=True,
            )
            stmt = select(exists().where(User.username == user.username))
            # 如果用户不存在，则添加用户
            if not db.scalar(stmt):
                db.add(user)
            else:
                return general_return_json(400, f"用户 {user.username} 已存在")
        db.commit()
        logging.info(f"导入用户文件{file.filename}")
        return general_return_json(200, "ok")
    except Exception as e:
        db.rollback()
        logging.error(f"导入用户文件 {file.filename}  失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"读取失败，请检查文件内容: {str(e)}")
