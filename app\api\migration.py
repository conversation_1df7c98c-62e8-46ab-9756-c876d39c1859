#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
数据迁移 API 路由模块

提供数据迁移的 REST API 接口
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, BackgroundTasks, Request, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.assets.public_assets import general_return_json
from app.core.database import get_db
from app.decorator.auth import required_login
from app.services.migration_service import migration_service
from app.models.models import User

router = APIRouter(prefix="/migration", tags=["数据迁移"])

class MigrationRequest(BaseModel):
    """迁移请求模型"""
    tables: Optional[List[str]] = None
    batch_size: Optional[int] = 100000

class MigrationResponse(BaseModel):
    """迁移响应模型"""
    job_id: str
    message: str

@router.post("/start", summary="启动数据迁移")
@required_login(roles=["admin", "super_admin"])
async def start_migration(
    request: Request,
    db: Session = Depends(get_db)
):
    """启动数据迁移任务
    
    Args:
        request: FastAPI 请求对象
        db: 数据库会话
    
    Returns:
        general_return_json: 返回结果
    """
    try:
        user = db.get(User, request.session.get("auth").get("id"))
        operator = user.nickname if user else "unknown"
        
        # 启动异步迁移任务
        job_id = migration_service.start_migration_task(operator)
        
        logging.info(f"用户 {operator} 启动了数据迁移任务: {job_id}")
        
        return general_return_json(
            200, 
            "数据迁移任务已启动，请稍后查看结果",
            {
                "job_id": job_id,
                "operator": operator,
                "tables": migration_service.migration_tables,
                "batch_size": migration_service.batch_size
            }
        )
        
    except Exception as e:
        logging.error(f"启动迁移任务失败: {str(e)}")
        return general_return_json(500, f"启动失败: {str(e)}")

@router.post("/table/{table_name}", summary="迁移单个表")
# @required_login(roles=["admin", "super_admin"])
async def migrate_single_table(
    table_name: str,
    request: Request,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """迁移单个表
    
    Args:
        table_name: 表名
        request: FastAPI 请求对象
        background_tasks: 后台任务
        db: 数据库会话
    
    Returns:
        general_return_json: 返回结果
    """
    try:
        user = db.get(User, request.session.get("auth").get("id"))
        operator = user.nickname if user else "unknown"
        
        # 检查表名是否在允许列表中
        if table_name not in migration_service.migration_tables:
            return general_return_json(
                400, 
                f"表 {table_name} 不在允许迁移的表列表中"
            )
        
        # 在后台执行单表迁移
        background_tasks.add_task(
            migration_service.run_migration,
            table_name=table_name,
            db=db,
            operator=operator
        )
        
        logging.info(f"用户 {operator} 启动了表 {table_name} 的迁移任务")
        
        return general_return_json(
            200, 
            f"表 {table_name} 迁移任务已启动",
            {"table": table_name, "operator": operator}
        )
        
    except Exception as e:
        logging.error(f"启动单表迁移失败: {str(e)}")
        return general_return_json(500, f"启动失败: {str(e)}")

@router.get("/history", summary="获取迁移历史")
# @required_login(roles=["admin", "super_admin"])
async def get_migration_history(
    limit: int = Query(10, ge=1, le=50, description="返回记录数量")
):
    """获取迁移历史记录
    
    Args:
        limit: 返回记录数量限制
    
    Returns:
        general_return_json: 返回结果
    """
    try:
        history = migration_service.get_migration_history(limit)
        return general_return_json(200, "获取成功", history)
        
    except Exception as e:
        logging.error(f"获取迁移历史失败: {str(e)}")
        return general_return_json(500, f"获取失败: {str(e)}")

@router.get("/status", summary="获取迁移状态")
# @required_login(roles=["admin", "super_admin"])
async def get_migration_status():
    """获取当前迁移状态
    
    Returns:
        general_return_json: 返回结果
    """
    try:
        # 获取最近一次迁移记录
        history = migration_service.get_migration_history(1)
        
        if not history:
            return general_return_json(200, "暂无迁移记录", {
                "status": "no_history",
                "tables": migration_service.migration_tables
            })
        
        latest = history[0]
        return general_return_json(200, "获取成功", {
            "latest_migration": latest,
            "available_tables": migration_service.migration_tables
        })
        
    except Exception as e:
        logging.error(f"获取迁移状态失败: {str(e)}")
        return general_return_json(500, f"获取失败: {str(e)}")

@router.get("/job/{job_id}", summary="获取任务状态")
@required_login(roles=["admin", "super_admin"])
async def get_job_status(job_id: str):
    """获取指定任务的状态
    
    Args:
        job_id: 任务ID
    
    Returns:
        general_return_json: 返回结果
    """
    try:
        status = migration_service.get_task_status(job_id)
        return general_return_json(200, "获取成功", status)
        
    except Exception as e:
        logging.error(f"获取任务状态失败: {str(e)}")
        return general_return_json(500, f"获取失败: {str(e)}")
