import logging

from sqlalchemy.orm import Session

from app.core.database import get_db
from app.dict import command
from app.external_service import qq_websocket_channel, wechat_websocket_channel
from app.models.models import Group, Robot, GroupRobot


async def job_syn_group_member():
    db_gen = get_db()
    db: Session = next(db_gen)
    try:
        logging.info("正在执行群成员列表更新")

        robots = db.query(Robot.account, Robot.type, Group.number).join(GroupRobot, Robot.id == GroupRobot.robot_id).join(Group, Group.id == GroupRobot.group_id) \
        .filter(
            Group.listening.is_(True),
            Robot.enable.is_(True),
            Robot.status.is_(True)
        ).all()

        for each in robots:
            if each[1] == 'QQ':
                await qq_websocket_channel.send_message_to_robot(robot=each[0], message=command.synGroupMemberPayload(each[2]))
            if each[1] == '微信':
                await wechat_websocket_channel.send_message_to_robot(robot=each[0],message=command.synGroupMemberPayload(each[2]))

    except Exception as e:
        logging.error(f"执行群成员列表更新失败：{str(e)}")
    finally:
        db.close()
