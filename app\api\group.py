#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2019 Tianxiadatong, Co.,Ltd. All Rights Reserved.

This module implements the FastAPI sub-router for the /group endpoint,
including related business logic.

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06
"""
import asyncio
import io
import logging

from fastapi import Query
import pandas as pd
from fastapi import APIRouter, Request, Depends, HTTPException, UploadFile, File
from fastapi.encoders import jsonable_encoder
from requests.compat import chardet
from sqlalchemy import select, func, or_
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from app.assets.public_assets import general_return_json
from app.core.database import get_db
from app.corn import mission
from app.decorator.auth import required_login
from app.dict import command
from app.external_service import wechat_websocket_channel, qq_websocket_channel
from app.models.models import Group, Robot, GroupMember, Production, GroupTrader, GroupProduction, Consigner, \
    GroupConsigner, User, GroupRobot, BbTFundInfo, Log
from app.schemas.schemas import GroupMemberFilter, GroupModify, \
    GroupMemberBatchUpdate

router = APIRouter(prefix="/group")


@router.post("/modify/listening", summary="改群jianting")
@required_login(roles=["admin", "super_admin"])
async def modify_listening(request: Request, group_data: GroupModify, db: Session = Depends(get_db)):
    user = db.get(User, request.session.get("auth").get("id"))
    log = Log(
        operator=user.nickname,
        target=f"群ID-{group_data.id}->{group_data.listening}",
        action="修改群监听操作",
        object="聊天群",
        result=False,
    )
    try:
        group = db.get(Group, group_data.id)

        if not group:
            return general_return_json(404, "不存在组")

        if group_data.listening is not None:
            group.listening = group_data.listening

            if group_data.listening is True:
                db.query(GroupRobot).filter(GroupRobot.group_id == group_data.id).delete()
                # 遍历 JSON 格式的 robot 列表（如 [1, 2, 3]）
                for robot_id in group.robot:
                    db.add(GroupRobot(group_id=group_data.id, robot_id=robot_id))

            if group_data.listening is False:
                db.query(GroupRobot).filter(GroupRobot.group_id == group.id).delete(synchronize_session=False)
            log.result = True
            log.target += f"修改群{group.id}的监听状态为{group_data.listening}"
            db.add(log)

            db.commit()
            return general_return_json(200, "ok", {})
    except HTTPException as ex:
        raise ex
    except Exception as e:
        db.rollback()
        log.result = True
        log.clause += f"修改群组启用状态：{e}"
        db.add(log)

        db.commit()
        logging.error(f"修改群组启用状态：{e}")
        raise HTTPException(status_code=500, detail=f"修改失败: {str(e)}")


@router.post("/modify", summary="改群信息和关联事物")
@required_login(roles=["admin", "super_admin"])
async def modify(request: Request, group_data: GroupModify, db: Session = Depends(get_db)):
    """修改群组信息和关联数据。

    Args:
        request (Request): FastAPI 请求对象
        group_data (GroupModify): 群组修改数据模型
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 修改成功或无更新
            - 404: 群组不存在
            - 400: 交易员验证失败

    Raises:
        HTTPException: 数据库操作异常
    """
    group = db.get(Group, group_data.id)
    user = db.get(User, request.session.get("auth").get("id"))
    log = Log(
        operator=user.nickname,
        target="",
        action="修改群关联数据操作",
        object="聊天群",
        result=False,
    )

    if not group:
        log.result = False
        log.clause = f'不存在组 {group.id}'
        db.add(log)
        db.commit()
        return general_return_json(404, "不存在组")

    updated = False

    try:
        if group_data.name is not None:
            group.name = group_data.name
            db.commit()
            updated = True

        if group_data.listened_robot is not None:
            db.query(GroupRobot).filter(GroupRobot.group_id == group.id).delete()
            for robot in group_data.listened_robot:
                robot_id = robot['id'] if isinstance(robot, dict) else robot.id
                db.add(GroupRobot(group_id=group_data.id, robot_id=robot_id))
            updated = True
            db.commit()

        # 更新 consigners
        if group_data.consigner is not None:
            db.query(GroupConsigner).filter(GroupConsigner.group_id == group.id).update(
                {GroupConsigner.is_deleted: True})
            if len(group_data.consigner) > 0:
                for each in group_data.consigner:
                    if db.query(Consigner).filter(Consigner.id == each['id']).first():
                        db.add(GroupConsigner(group_id=group_data.id, consigner_id=each['id']))
                    else:
                        return general_return_json(409, f"{each['id']} 不是委托员")
            db.commit()
            updated = True

        # 更新 traders
        if group_data.trader is not None:
            db.query(GroupTrader).filter(GroupTrader.group_id == group.id).update({GroupTrader.is_deleted: True})
            for trader in group_data.trader:
                trader_id = trader['id'] if isinstance(trader, dict) else trader.id
                trader_user = db.query(GroupMember).filter(GroupMember.id == trader_id).first()
                if not trader_user or trader_user.role != 'trader':
                    return general_return_json(400, f"用户 {trader_id} 不是交易员")
                db.add(GroupTrader(group_id=group_data.id, trader_id=trader_id))
            db.commit()
            updated = True

        # 更新产品
        if group_data.product is not None:
            db.query(GroupProduction).filter(GroupProduction.group_id == group.id).update(
                {GroupProduction.is_deleted: True})
            for product in group_data.product:
                fund_code = product.fund_code if product.fund_code else product.production_code  # 使用属性访问
                real_prod = db.query(BbTFundInfo).filter(BbTFundInfo.fund_code == fund_code).first()
                stored_prod = db.query(Production).filter(Production.production_code == fund_code).first()
                if real_prod:
                    db.add(GroupProduction(group_id=group_data.id, production_id=fund_code))
                    if not stored_prod:
                        db.add(Production(
                            production_code=fund_code,
                            production=real_prod.fund_name
                        ))
            updated = True
            db.commit()

        # 更新机器人 ID 列表
        if group_data.robot is not None:
            group.robot = [r['id'] if isinstance(r, dict) else r.id for r in group_data.robot]
            updated = True
            db.commit()

        if updated:
            log.result = True
            log.target += f"修改群{group.id}的关联信息为{group_data.listening}"
            db.add(log)

            db.commit()
            logging.info(f"群组\"{group.name}\"信息已被更改")
            return general_return_json(200, "ok")
        else:
            db.rollback()
            return general_return_json(200, "not updated")

    except HTTPException as ex:
        raise ex
    except Exception as e:
        db.rollback()
        log.result = False
        log.clause = str(e)
        db.add(log)
        db.commit()
        logging.error(f"修改群组信息和关联数据出错：{e}")
        raise HTTPException(status_code=500, detail=f"修改失败: {str(e)}")


@router.get("/consigner", summary="看所有委托人，可以根据group_id过滤")
@required_login(roles=["admin", "salesman", "super_admin"])
async def get_consigner(request: Request, db: Session = Depends(get_db), group: int = None):
    """获取委托人列表。

    Args:
        request (Request): FastAPI 请求对象
        db (Session): 数据库会话对象
        group (int, optional): 群组ID过滤

    Returns:
        general_return_json: 返回结果
            - 200: 返回委托人列表

    Raises:
        HTTPException: 数据库操作异常
    """
    try:
        consigners = db.query(Consigner).all()
        return general_return_json(200, "ok", consigners)
    except HTTPException as ex:
        raise ex
    except Exception as e:
        logging.error(f"获取委托人列表出错：{e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


@router.get("/trader", summary="看所有交易员")
@required_login(roles=["admin", "salesman", "super_admin"])
async def association_trader(request: Request, id: int, db: Session = Depends(get_db)):
    """获取交易员列表。
    Args:
        request (Request): FastAPI 请求对象
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 返回交易员列表

    Raises:
        HTTPException: 数据库操作异常
    """
    try:
        traders = db.query(GroupMember).where(GroupMember.group_id == id, GroupMember.role == 'trader').all()
        return general_return_json(200, "ok", traders)
    except HTTPException as ex:
        raise ex
    except Exception as e:
        logging.error(f"获取交易员列表出错：{e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


@router.get("/production/{group_id}", summary="获取该群关联产品列表")
@required_login(roles=["admin", "salesman", "super_admin"])
async def association_production(request: Request, group_id: int, db: Session = Depends(get_db)):
    """获取该群关联产品列表。

    Args:
        request (Request): FastAPI 请求对象
        group_id (int): 群组ID
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 返回产品列表

    Raises:
        HTTPException: 数据库操作异常
    """
    try:
        production_ids = db.query(GroupProduction.production_id).filter(
            GroupProduction.group_id == group_id,
            GroupProduction.is_deleted == False
        ).all()

        production_ids = [str(prod.production_id) for prod in production_ids]

        logging.info(f"Fetching products for group {group_id} with production IDs: {production_ids}")

        products = db.query(BbTFundInfo).filter(BbTFundInfo.fund_code.in_(production_ids)).all()

        if not products:
            logging.info(f"No products found for group {group_id} with production IDs: {production_ids}")
        else:
            logging.info(f"Found {len(products)} products for group {group_id} with production IDs: {production_ids}")
        return general_return_json(200, "ok", products)
    except HTTPException as ex:
        raise ex
    except Exception as e:
        logging.error(f"获取关联产品列表出错：{e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


@router.get("/production", summary="看所有关联产品")
@required_login(roles=["admin", "salesman", "super_admin"])
async def association_production(
        request: Request,
        db: Session = Depends(get_db),
        page: int = Query(1),
        size: int = Query(10),
        keyword: str = Query('')
):
    """
    获取所有关联产品列表（分页 + 模糊搜索）。
    """
    try:
        query = db.query(BbTFundInfo)

        # ✅ 仅当 keyword 非空时才做模糊查询
        if keyword.strip():
            like_pattern = f"%{keyword.strip()}%"
            query = query.filter(
                or_(
                    BbTFundInfo.fund_name.ilike(like_pattern),
                    BbTFundInfo.fund_code.ilike(like_pattern)
                )
            )

        total = query.count()
        prods = query.offset((page - 1) * size).limit(size).all()

        return general_return_json(
            200,
            "ok",
            {
                "total": total,
                "page": page,
                "size": size,
                "items": prods
            }
        )

    except HTTPException as ex:
        raise ex
    except Exception as e:
        logging.error(f"获取所有产品出错：{e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


async def read_csv_async(filepath):
    df = await asyncio.to_thread(pd.read_csv, filepath)
    return df


@router.post("/import", summary="开导")
@required_login(roles=["admin", "super_admin"])
async def import_group(request: Request, db: Session = Depends(get_db), file: UploadFile = File(...)):
    """导入群组数据（CSV文件）。

    Args:
        request (Request): FastAPI 请求对象
        db (Session): 数据库会话对象
        file (UploadFile): 上传的CSV文件

    Returns:
        general_return_json: 返回结果
            - 200: 导入成功
            - 400: 文件格式错误

    Raises:
        HTTPException: 文件读取或数据库操作异常
    """
    user = db.get(User, request.session.get("auth").get("id"))
    log = Log(
        operator=user.nickname,
        target=str(file.filename),
        action="导入群操作",
        object="聊天群",
        result=False,
    )

    def safe_str_strip(val):
        if val is None:
            return ''
        try:
            return str(val).strip()
        except Exception:
            return ''

    if not file.filename.endswith('.csv'):
        log.result = False
        log.clause = "仅支持CSV文件"
        db.add(log)

        db.commit()
        return general_return_json(400, "仅支持 CSV 文件")

    try:
        # 读取 CSV 文件内容（GBK 编码）
        content = await file.read()
        # 自动检测编码
        result = chardet.detect(content)
        detected_encoding = result['encoding']
        confidence = result['confidence']

        print(f"检测到编码: {detected_encoding}, 置信度: {confidence}")

        # 解码为字符串并读取为 DataFrame

        df = await asyncio.to_thread(
            lambda: pd.read_csv(io.StringIO(content.decode(detected_encoding)), dtype=str)
        )
        columns = df.columns.tolist()

        rows = df.values.tolist()

        if len(columns) % 2 != 0:
            return general_return_json(400, "请确保列完整，且产品名称N和产品代码N总是成对出现", {})

        for idx, row in enumerate(rows):
            if len(row) < 4:
                return general_return_json(400, f"第 {idx + 1} 行格式不完整")

            group_type = row[0]
            group_name = row[1]
            group_account = row[2]
            in_group_robot_account = row[3]

            products = []

            # 从第4列开始，每次跳2列（产品名称和产品代码）
            for col_idx in range(4, min(len(columns) - 1, len(row) - 1), 2):
                product_name = safe_str_strip(row[col_idx])
                product_code = safe_str_strip(row[col_idx + 1])

                if product_name.lower() != 'nan' and product_code.lower() != 'nan' and product_name and product_code:
                    products.append({
                        'fund_name': product_name,
                        'fund_code': product_code
                    })

            fetch_robot = db.query(Robot).filter(
                Robot.account == in_group_robot_account,
                Robot.type == group_type
            ).first()

            if not fetch_robot:
                return general_return_json(400, f"未找到 {group_type} 机器人:{in_group_robot_account}")

            fetch_group = db.query(Group).filter(
                Group.type == group_type,
                Group.number == group_account
            ).first()

            if not fetch_group:
                build_group = Group(
                    type=group_type,
                    name=group_name,
                    number=group_account,
                    listening=False,
                    robot=[fetch_robot.id]
                )
                db.add(build_group)
                db.flush()
                group_id = build_group.id
            else:
                return general_return_json(409, f"已存在相同的组:{group_account}")

            for each in products:

                if not db.query(BbTFundInfo).filter(
                        BbTFundInfo.fund_name == each['fund_name'],
                        BbTFundInfo.fund_code == each['fund_code']
                ).first():
                    db.rollback()
                    return general_return_json(400,
                                               f"请检查，不存在产品名称/代号：{each['fund_name']}-{each['fund_code']}")

                if not db.query(GroupProduction).filter(
                        GroupProduction.group_id == group_id,
                        GroupProduction.production_id == each['fund_code']
                ).first():
                    build_group_production = GroupProduction(
                        group_id=group_id,
                        production_id=each['fund_code'],
                    )
                    db.add(build_group_production)

                db.query(Production).filter(Production.production_code == each['fund_code']).delete()
                db.add(Production(
                    production_code=each['fund_code'],
                    production=each['fund_name'],
                ))
                db.flush()

        # 同步群成员（机器人在线检查）
        for idx, row in enumerate(rows):
            if len(row) < 4:
                continue

            group_type = row[0]
            group_account = row[2]
            in_group_robot_account = row[3]

            if group_type == 'QQ':
                if in_group_robot_account in qq_websocket_channel.active_connections:
                    await qq_websocket_channel.send_message_to_robot(
                        robot=in_group_robot_account,
                        message=command.synGroupMemberPayload(group_account)
                    )
                else:
                    db.rollback()
                    return general_return_json(500, f"{group_type} 机器人 {in_group_robot_account} 离线")
            elif group_type == '微信':
                if in_group_robot_account in wechat_websocket_channel.active_connections:
                    await wechat_websocket_channel.send_message_to_robot(
                        robot=in_group_robot_account,
                        message=command.synGroupMemberPayload(group_account)
                    )
                else:
                    db.rollback()
                    return general_return_json(500, f"{group_type} 机器人 {in_group_robot_account} 离线")
        db.commit()
        await mission.job_syn_group_member()
        log.result = True
        db.add(log)

        db.commit()
        return general_return_json(200, "导入成功，稍后将获取群成员列表...")
    except Exception as e:
        db.rollback()
        log.result = False
        log.clause = str(e)
        db.add(log)

        db.commit()
        logging.error(f"文件 {file.filename} 导入失败：{e}")
        raise HTTPException(status_code=500, detail=f"读取失败，请检查文件内容: {str(e)}")


@router.post("/member/modify",
             summary="批量修改群成员，其中权限值范围0-7，从左到右三位二进制数分别代表捞单、咨询、询价权限")
@required_login(roles=["admin", "super_admin"])
async def modify_permission(request: Request, update_data: GroupMemberBatchUpdate, db: Session = Depends(get_db)):
    """批量修改群成员权限。

    权限值范围0-7，从左到右三位二进制数分别代表捞单、咨询、询价权限。

    Args:
        request (Request): FastAPI 请求对象
        update_data (GroupMemberBatchUpdate): 批量更新数据模型
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 更新成功或无需更新
            - 404: 成员不存在
            - 500: 修改失败

    Raises:
        HTTPException: 数据库操作异常
    """
    user = db.get(User, request.session.get("auth").get("id"))
    log = Log(
        operator=user.nickname,
        target="",
        action="修改群成员操作",
        object="聊天群",
        result=False,
    )
    db.add(log)
    db.commit()
    try:
        # 补每个成员的 group_id（从外层统一赋值）
        for member in update_data.member:
            member.group_id = update_data.group_id

        # 使用集合来存储成员ID，提高查找效率
        member_ids = {member.id for member in update_data.member}

        # 单次查询获取所有相关成员
        db_members = (
            db.query(GroupMember)
            .filter(
                GroupMember.id.in_(member_ids),
                GroupMember.group_id == update_data.group_id
            )
            .all()
        )

        # 使用字典存储数据库成员，避免重复查找
        db_members_dict = {m.id: m for m in db_members}

        # 验证成员存在性和权限
        missing_ids = member_ids - db_members_dict.keys()
        if missing_ids:
            return general_return_json(404, f"以下成员ID不存在或不属于该群组：{', '.join(map(str, missing_ids))}")

        # 批量更新权限与角色
        updates = []
        for member in update_data.member:

            db_member = db_members_dict.get(member.id)

            print(member)
            print(db_member)

            if db_member:
                updated = False
                if db_member.permission != member.permission:
                    db_member.permission = member.permission
                    updated = True
                if db_member.role != member.role:
                    db_member.role = member.role
                    updated = True
                if updated:
                    updates.append(member.id)

        if updates:
            db.commit()
            log.result = True
            log.target += f"更新了 {len(updates)} 个成员的权限和角色"
            db.add(log)

            db.commit()
            logging.info(f"更新了 {len(updates)} 个成员的权限和角色")
            return general_return_json(200, f"成功更新 {len(updates)} 个成员的权限和角色")
        return general_return_json(200, "没有需要更新的内容")

    except Exception as e:
        db.rollback()
        db.commit()
        log.result = False
        log.clause = f"批量更新群员信息出错: {str(e)}"
        db.add(log)
        logging.error(f"批量更新群员信息出错: {str(e)}")
        return general_return_json(500, f"修改失败: {str(e)}")


# 获取群员，可过滤，必填群ID
@router.get("/member", summary="get群员，可过滤，参数ID，")
@required_login(roles=["admin", "salesman", "super_admin"])
async def get_all_members(request: Request, db: Session = Depends(get_db), filtered: GroupMemberFilter = Depends()):
    """获取群成员列表。

    Args:
        request (Request): FastAPI 请求对象
        db (Session): 数据库会话对象
        filtered (GroupMemberFilter): 过滤条件

    Returns:
        general_return_json: 返回结果
            - 200: 返回群成员列表和总数

    Raises:
        HTTPException: 数据库操作异常
    """
    try:
        # 构建查询
        query = select(GroupMember).where(GroupMember.group_id == filtered.id)

        if filtered.keyword:
            query = query.where(GroupMember.name.like(f"%{filtered.keyword}%"))

        # 获取总数
        total = db.scalar(select(func.count()).select_from(query.subquery()))

        # 分页参数
        page_size = 5000
        offset = (filtered.page - 1) * page_size

        # 查询成员
        members = db.execute(query.offset(offset).limit(page_size)).scalars().all()

        # 提取成员名和号码
        names = [m.name for m in members]
        numbers = [m.number for m in members]

        # 查询机器人
        robots = db.query(Robot.nickname).filter(Robot.nickname.in_(names)).all()
        robot_names = set(r.nickname for r in robots)

        # 查询交易员
        users = db.query(User.qq, User.wechat).filter(
            or_(User.qq.in_(numbers), User.wechat.in_(numbers))
        ).all()

        trader_numbers = set()
        for u in users:
            if u.qq:
                trader_numbers.add(u.qq)
            if u.wechat:
                trader_numbers.add(u.wechat)

        # 构造结果
        result = []
        for m in members:
            result.append({
                "id": m.id,
                "name": m.name,
                "number": m.number,
                "role": m.role,
                "permission": m.permission,
            })

        return general_return_json(200, "ok", {
            "total": total,
            "data": result
        })

    except SQLAlchemyError as e:
        # 数据库相关异常
        db.rollback()
        return general_return_json(500, "数据库错误", {"error": str(e)})

    except Exception as e:
        # 其他未预料异常
        return general_return_json(500, "服务器内部错误", {"error": str(e)})


# 按照过滤条件，获取所有组，可选：群类型/启用or禁用/全模糊群名/全模糊群号/机器人有无异常
@router.get("/")
@required_login(roles=["admin", "salesman", "super_admin"])
async def get_filtered_group(request: Request, db: Session = Depends(get_db)):
    """获取群组列表。

    支持按群类型、启用状态、群名、群号、机器人状态等条件过滤。

    Args:
        request (Request): FastAPI 请求对象
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 返回群组列表

    Raises:
        HTTPException: 数据库操作异常
    """
    # 主查询获取所有群组
    try:
        query = db.query(Group).order_by(Group.id)

        result = []
        for group in query.all():

            group_dict = jsonable_encoder(group)

            # 获取并处理机器人信息
            robot_ids = group.robot or []
            robot_infos = []
            if robot_ids:
                robots = db.query(Robot.id, Robot.nickname, Robot.status, Robot.account).filter(
                    Robot.id.in_(robot_ids)).all()
                robot_infos = [
                    {
                        "id": rid,
                        "nickname": nick,
                        "status": stat,
                        "account": acc
                    } for rid, nick, stat, acc in robots
                ]

            # 获取监听机器人
            listened_robots = []
            listened_robots_data = (
                db.query(Robot.id, Robot.nickname, Robot.status, Robot.account)
                .join(GroupRobot, GroupRobot.robot_id == Robot.id)
                .filter(GroupRobot.group_id == group.id)
                .all()
            )
            for rid, nickname, status, account in listened_robots_data:
                listened_robots.append({
                    "id": rid,
                    "nickname": nickname,
                    "status": status,
                    "account": account
                })

            # 获取产品信息
            products = []
            products_data = (
                db.query(Production.id, Production.production, Production.production_code)
                .join(GroupProduction, GroupProduction.production_id == Production.production_code)
                .filter(GroupProduction.group_id == group.id, GroupProduction.is_deleted == False)
                .all()
            )
            for pid, name, code in products_data:
                products.append({
                    "id": pid,
                    "name": name,
                    "production_code": code
                })

            # 获取交易员信息
            traders = []

            traders_data = (
                db.query(GroupMember.id, GroupMember.name, GroupMember.number)
                .join(GroupTrader, GroupMember.id == GroupTrader.trader_id)
                .filter(GroupTrader.group_id == group.id, GroupTrader.is_deleted == False)
                .all()
            )
            for tid, name, account in traders_data:
                traders.append({
                    "id": tid,
                    "name": name,
                    "account": account,
                    "fund_code": tid,
                    "fund_name": name
                })

            # 获取委托人信息
            consigners = []
            consigners_data = (
                db.query(Consigner.id, Consigner.consigner)
                .join(GroupConsigner, GroupConsigner.consigner_id == Consigner.id)
                .filter(GroupConsigner.group_id == group.id, GroupConsigner.is_deleted == False)
                .all()
            )
            for cid, name in consigners_data:
                consigners.append({
                    "id": cid,
                    "name": name
                })

            group_dict["robot"] = robot_infos
            group_dict["listened_robot"] = listened_robots
            group_dict["product"] = products
            group_dict["trader"] = traders
            group_dict["consigner"] = consigners

            result.append(group_dict)

        return general_return_json(200, "ok", {
            "data": result,
        })
    except Exception as e:
        db.rollback()
        logging.error(f"获取群出错: {str(e)}")
        return general_return_json(500, f"获取失败: {str(e)}")


@router.post("/consginer/import", summary="开导委托人")
@required_login(roles=["root", "super_admin"])
async def import_group(request: Request, db: Session = Depends(get_db), file: UploadFile = File(...)):
    """导入群组数据（CSV文件）。

    Args:
        request (Request): FastAPI 请求对象
        db (Session): 数据库会话对象
        file (UploadFile): 上传的CSV文件

    Returns:
        general_return_json: 返回结果
            - 200: 导入成功
            - 400: 文件格式错误

    Raises:
        HTTPException: 文件读取或数据库操作异常
    """
    user = db.get(User, request.session.get("auth").get("id"))
    log = Log(
        operator=user.nickname,
        target=str(file.filename),
        action="导入委托人操作",
        object="聊天群",
        result=False,
    )

    def safe_str_strip(val):
        if val is None:
            return ''
        try:
            return str(val).strip()
        except Exception:
            return ''

    if not file.filename.endswith('.csv'):
        return general_return_json(400, "仅支持 CSV 文件")

    try:
        content = await file.read()

        result = chardet.detect(content)
        detected_encoding = result['encoding']
        confidence = result['confidence']

        print(f"检测到编码: {detected_encoding}, 置信度: {confidence}")

        df = pd.read_csv(io.StringIO(content.decode(detected_encoding)), dtype=str)
        rows = df.values.tolist()

        for idx, row in enumerate(rows):
            db.add(Consigner(
                consigner=row[0]
            ))
            db.flush()
        db.commit()
        log.result = True
        db.add(log)

        db.commit()

        return general_return_json(200, "ok")

    except Exception as e:
        db.rollback()
        log.clause = str(e)
        log.result = False
        db.add(log)

        db.commit()
        raise HTTPException(status_code=500, detail=str(e))


# 获取机器人监听的群
@router.get("/wx/listening", summary="获取现在监听的群聊名单")
async def get_listening_groups(request: Request, db: Session = Depends(get_db)):
    """获取当前监听的微信群聊名单。

    Args:
        request (Request): FastAPI 请求对象
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 返回当前监听的微信群聊名单

    Raises:
        HTTPException: 数据库操作异常
    """
    try:
        # 查询所有监听的微信群聊
        robot_name = request.query_params.get("robot_name")
        if not robot_name:
            return general_return_json(400, "机器人名称不能为空")

        # 获取机器人 id， like
        robot = db.query(Robot).filter(
            Robot.nickname.ilike(f"%{robot_name}%")
        ).first()

        if not robot:
            return general_return_json(404, "机器人不存在")

        group_ids_with_info = db.query(Group).join(
            GroupRobot,
            GroupRobot.group_id == Group.id
        ).filter(
            GroupRobot.robot_id == robot.id
        ).all()

        if not group_ids_with_info:
            return general_return_json(404, "没有找到监听的微信群聊")

        group_names = [group.name for group in group_ids_with_info]
        return general_return_json(200, "ok", {
            "listening_groups": group_names
        })

    except HTTPException as ex:
        return general_return_json(500, f"获取失败: {str(ex)}")
