#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2019 Tianxiadatong, Co., Ltd. All Rights Reserved.

This module implements WebSocket channel for WeChat robot communication.

# TODO: 完成微信机器人 WebSocket 通道的完整实现$UwU(y)$202507-03$

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06
"""
import logging
from typing import Dict
from fastapi import APIRouter
from sqlalchemy.orm import Session
from starlette.websockets import WebSocket, WebSocketDisconnect

from app.core.database import get_db
from app.dict import command
from app.models.models import Robot

router = APIRouter(prefix="/wx")

active_connections: Dict[str, WebSocket] = {}

@router.websocket("/tunnel/{robot}")
async def websocket_endpoint(websocket: WebSocket,robot:str):
    await websocket.accept()
    db_gen = get_db()
    db: Session = next(db_gen)
    try:
        db.query(Robot).filter(Robot.account == robot).update({Robot.status: True})
        db.commit()
    except Exception as e:
        db.rollback()
        logging.warning(f"机器人{robot}上线，但是无法修正机器人的在线状态:{str(e)}")
    finally:
        db.close()
    active_connections[robot] = websocket
    try:
        while True:
            data = await websocket.receive_json()
            await command.resolveAndExecuteWechatCommand(data, robot)
    except WebSocketDisconnect:
        print("客户端断开连接")
        db_gen = get_db()
        db: Session = next(db_gen)
        try:
            db.query(Robot).filter(Robot.account == robot).update({Robot.status: False})
            db.commit()
        except Exception as e:
            db.rollback()
            logging.warning(f"机器人{robot}下线，但是无法修正机器人的在线状态:{str(e)}")
        finally:
            db.close()

        active_connections.pop(robot, None)

async def send_message_to_robot(robot: str, message: dict):
    websocket = active_connections.get(robot)
    if websocket:
        try:
            await websocket.send_json(message)
        except Exception as e:
            print(f"发送给 {robot} 失败：{e}")
            active_connections.pop(robot, None)
    else:
        print(f"未找到 {robot} 的 WebSocket 连接")