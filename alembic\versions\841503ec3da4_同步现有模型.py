"""同步现有模型

Revision ID: 841503ec3da4
Revises: 25fd545291b6
Create Date: 2025-06-24 18:33:31.187164

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '841503ec3da4'
down_revision: Union[str, Sequence[str], None] = '25fd545291b6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ewx_group',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('robot', sa.Integer(), nullable=False),
    sa.Column('groupAnnouncement', sa.Text(), nullable=True),
    sa.Column('groupName', sa.String(length=255), nullable=False),
    sa.Column('createTime', sa.Date(), nullable=False),
    sa.Column('level', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ewx_group_id'), 'ewx_group', ['id'], unique=False)
    op.create_table('ewx_message',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('date', sa.DateTime(), nullable=False),
    sa.Column('atme', sa.Boolean(), nullable=False),
    sa.Column('type', sa.Integer(), nullable=False),
    sa.Column('sender', sa.String(length=100), nullable=False),
    sa.Column('receiver', sa.String(length=100), nullable=False),
    sa.Column('position', sa.String(length=255), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('ewx_robot',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('robotId', sa.String(length=255), nullable=False),
    sa.Column('name', sa.String(length=150), nullable=False),
    sa.Column('corporation', sa.String(length=150), nullable=False),
    sa.Column('sumInfo', sa.String(length=255), nullable=False),
    sa.Column('openCallback', sa.Boolean(), nullable=False),
    sa.Column('encryptType', sa.Boolean(), nullable=False),
    sa.Column('callbackUrl', sa.String(length=255), nullable=False),
    sa.Column('enableAdd', sa.Boolean(), nullable=False),
    sa.Column('replyAll', sa.Boolean(), nullable=False),
    sa.Column('robotKeyCheck', sa.Boolean(), nullable=False),
    sa.Column('callBackRequestType', sa.Integer(), nullable=False),
    sa.Column('robotType', sa.Boolean(), nullable=False),
    sa.Column('firstLogin', sa.Date(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('authExpir', sa.Date(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ewx_robot_id'), 'ewx_robot', ['id'], unique=False)
    op.create_table('qq_group',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('group_id', sa.String(length=100), nullable=False),
    sa.Column('group_name', sa.String(length=255), nullable=False),
    sa.Column('member', sa.Integer(), nullable=False),
    sa.Column('max_member', sa.Integer(), nullable=False),
    sa.Column('robot', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_qq_group_id'), 'qq_group', ['id'], unique=False)
    op.create_table('qq_robot',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('account', sa.String(length=100), nullable=False),
    sa.Column('nickname', sa.String(length=255), nullable=False),
    sa.Column('personal_note', sa.Text(), nullable=True),
    sa.Column('sex', sa.Boolean(), nullable=False),
    sa.Column('address', sa.String(length=255), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('bearer', sa.String(length=100), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_qq_robot_id'), 'qq_robot', ['id'], unique=False)
    op.alter_column('group_member', 'permission',
               existing_type=mysql.TINYINT(),
               type_=sa.SmallInteger(),
               existing_nullable=False)
    op.alter_column('script', 'content',
               existing_type=mysql.LONGTEXT(),
               type_=sa.Text(),
               existing_nullable=False)
    op.alter_column('script', 'environment',
               existing_type=mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_0900_ai_ci'),
               type_=sa.Text(),
               existing_nullable=False)
    op.create_index(op.f('ix_script_id'), 'script', ['id'], unique=False)
    op.alter_column('user', 'email',
               existing_type=mysql.VARCHAR(length=150),
               type_=sa.String(length=100),
               existing_nullable=False)
    op.create_index(op.f('ix_user_id'), 'user', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_id'), table_name='user')
    op.alter_column('user', 'email',
               existing_type=sa.String(length=100),
               type_=mysql.VARCHAR(length=150),
               existing_nullable=False)
    op.drop_index(op.f('ix_script_id'), table_name='script')
    op.alter_column('script', 'environment',
               existing_type=sa.Text(),
               type_=mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_0900_ai_ci'),
               existing_nullable=False)
    op.alter_column('script', 'content',
               existing_type=sa.Text(),
               type_=mysql.LONGTEXT(),
               existing_nullable=False)
    op.alter_column('group_member', 'permission',
               existing_type=sa.SmallInteger(),
               type_=mysql.TINYINT(),
               existing_nullable=False)
    op.drop_index(op.f('ix_qq_robot_id'), table_name='qq_robot')
    op.drop_table('qq_robot')
    op.drop_index(op.f('ix_qq_group_id'), table_name='qq_group')
    op.drop_table('qq_group')
    op.drop_index(op.f('ix_ewx_robot_id'), table_name='ewx_robot')
    op.drop_table('ewx_robot')
    op.drop_table('ewx_message')
    op.drop_index(op.f('ix_ewx_group_id'), table_name='ewx_group')
    op.drop_table('ewx_group')
    # ### end Alembic commands ###
