"""
from sqlalchemy import create_engine, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.dialects.postgresql.base import PGDialect

# KingBase 连接配置
database = "robot"
user = "root"
password = "root"
host = "localhost"
port = "54321"

# 构建连接字符串 - 使用 PostgreSQL 方言连接 KingBase
DATABASE_URL = f'postgresql+psycopg2://{user}:{password}@{host}:{port}/{database}'

@event.listens_for(PGDialect, 'do_connect')
def receive_do_connect(dialect, conn_rec, cargs, cparams):
    """处理数据库连接事件，设置 KingBase 兼容性"""
    # 设置固定的版本信息
    dialect.server_version_info = (9, 6, 0)
    # 覆盖版本检测方法
    dialect._get_server_version_info = lambda conn: (9, 6, 0)
    # 禁用某些可能不兼容的 PostgreSQL 特性
    dialect.supports_sane_rowcount = True
    dialect.supports_sane_multi_rowcount = True
    return None

engine = create_engine(
    DATABASE_URL,
    echo=True,
    pool_pre_ping=True,
    connect_args={
        "application_name": "TradeRobot"
    }
)

@event.listens_for(engine, 'connect')
def receive_connect(dbapi_connection, connection_record):
    """处理引擎连接事件"""
    # 设置会话参数
    cursor = dbapi_connection.cursor()
    cursor.execute("SET SESSION CHARACTERISTICS AS TRANSACTION ISOLATION LEVEL READ COMMITTED")
    cursor.close()

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

def get_db():
    db: Session = SessionLocal()
    try:
        yield db
    finally:
        db.close()
"""