import json
import logging

from fastapi import APIRouter, Request, Depends, Query
from sqlalchemy.orm import Session

from app.assets.public_assets import general_return_json
from app.core.database import get_db
from app.decorator.auth import required_login
from app.models.models import Group, Message

router = APIRouter(prefix="/message", tags=["message"])

@router.get("/")
@required_login(roles=["admin", "salesman", "super_admin"])
async def get_message_by_group(
    request: Request,
    db: Session = Depends(get_db),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    group_id: str = Query(None, description="group_ID（可选）")
):
    try:
        query = db.query(Message).join(Group, Message.position == Group.number).order_by(Message.date.desc())

        if group_id:
            query = query.filter(Group.id == group_id)

        total = query.count()
        msgs = query.offset((page - 1) * page_size).limit(page_size).all()


        return general_return_json(200, "ok", {
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": (total + page_size - 1) // page_size,
            "data": msgs
        })

    except Exception as e:
        logging.error(f"获取Messages出错: {str(e)}")
        return general_return_json(500, f"获取失败: {str(e)}")
