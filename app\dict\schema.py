#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2019 Tianxiadatong, Co., Ltd. All Rights Reserved.

This module only defines a standardized schema for transfer data in websocket.

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06
"""

from typing import Optional, Literal, ForwardRef, List

from pydantic import BaseModel, field_validator


class MessagePacket(BaseModel):
    sender: str
    receiver: str
    content: str
    position: str
    type:int
    reply:Optional[int] = None
    channel:str
    sequence:Optional[int] = None

    @field_validator("channel", mode="before")
    @classmethod
    def convert_channel(cls, v):
        return str(v)

# Forward reference for self-referential model
PromptRef = ForwardRef("Prompt")

class Prompt(BaseModel):
    role: Literal['user', 'robot']
    content_type: Literal['Text', 'Image']
    content: str
    task_id: Optional[str] = None
    img_base64: Optional[str] = None
    refer: Optional[List[PromptRef]] = None  # 正确的 self-reference 写法

# 更新前向引用
Prompt.update_forward_refs()