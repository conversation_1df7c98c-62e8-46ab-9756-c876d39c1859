#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2019 Tianxiadatong, Co., Ltd. All Rights Reserved.

This module is used for control WeChat robot via API endpoints,providing various operations.
It is now deprecated and kept for archival or compatibility purposes.

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06
"""


from typing import Dict
import requests
from fastapi import Depends
from sqlalchemy.orm import Session
from app.assets.enterprise_wechat_assets import send_message_url, modify_callback_url
from app.assets.public_assets import general_return_json
from app.core.database import get_db
from app.models.models import EwxRobot, EwxMessage
#from app.schemas.schemas import RobotActivityUpdate

#####################################
#           FBI WARNING             #
#    此为企业微信API以及业务部分代码     #
#               现已弃用             #
#                                   #
#####################################

# ###启/停机器人
# async def modify_robot_activity(robot:RobotActivityUpdate,db:Session = Depends(get_db)):
#     payload = {
#         "openCallback": 1 if robot.is_active else 0,
#     }
#
#     try:
#         query_robot = db.query(EwxRobot).filter(EwxRobot.id == robot.robot_id).first()
#
#         if query_robot is None:
#             return general_return_json(404,"该机器人不存在")
#
#         response = requests.post(json=payload,url=modify_callback_url+"?robotId="+query_robot.robotId)
#         response.raise_for_status()
#
#         if response.status_code != 200:
#             return response.json()
#
#         query_robot.is_active = robot.is_active #save acitivty to db
#         db.commit()
#         return general_return_json(200,"ok")
#     except Exception as e:
#         db.rollback()
#         return general_return_json(500,str(e))
#
# ###获取所有机器人信息
# async def get_all_robot_info(db: Session = Depends(get_db)):
#     return db.query(EwxRobot).all()
#
# ###发信息
# async def send_message(robot_token:str, receiver:str,message:str,db: Session = Depends(get_db)):
#     payload = {
#         "socketType":2,#fixed type
#         "list":[
#             {
#                 "type":203,#fixed type
#                 "titleList":[
#                     receiver
#                 ],
#                 "receivedContent":message
#             }
#         ]
#     }
#     response = requests.post(url=send_message_url+"?robotId="+robot_token, json=payload)
#     response.raise_for_status()
#
#     if response.status_code != 200:
#         return response.json()
#
#     try:
#         robot = db.query(EwxRobot).filter(EwxRobot.robotId == robot_token).first()
#         #save raw msg to db
#         box = EwxMessage(
#             sender=robot.name,
#             receiver=receiver,
#             message=message,
#             atme=False,
#             type=3,
#             position=receiver
#         )
#         db.add(box)
#         db.commit()
#         return general_return_json(200,"ok")
#     except Exception as e:
#         db.rollback()
#         return general_return_json(500,str(e))
#
# #收信息
# async def recv_messages(rawJson:Dict,identifier:str,db: Session = Depends(get_db)):
#         print(rawJson)
#         # TODO: 实现企业微信机器人对接功能$UwU(y)$202507-03$
#         try:
#             robot = db.query(EwxRobot).filter(EwxRobot.id == identifier).first()
#             #save db
#             message = EwxMessage(
#                 sender=rawJson["receivedName"],
#                 receiver=robot.name,
#                 message=rawJson["rawSpoken"],
#                 atme = False if rawJson["atMe"] == 'false' else True,
#                 type=rawJson["roomType"],
#                 position=rawJson["groupName"]
#             )
#             db.add(message)
#             db.commit()
#             await send_message(robot.robotId, receiver=message.position, message="@" + message.sender +" 收到了兄弟", db=db)
#         except Exception as e:
#             print(str(e))
#             db.rollback()