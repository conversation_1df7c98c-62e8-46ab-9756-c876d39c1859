#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
数据库初始化脚本。
用于创建所有数据库表。

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06
"""

import logging
from app.core.database import init_db
# 导入所有模型，确保它们被注册到 Base.metadata
from app.models.models import (
    BbTFundInfo,
    Task,
    Audit,
    Production,
    Consigner,
    Message,
    GroupMember,
    Group,
    Script,
    Robot,
    User
)

def main():
    """初始化数据库"""
    try:
        logging.info("开始初始化数据库...")
        init_db()
        logging.info("数据库初始化完成！")
    except Exception as e:
        logging.error(f"数据库初始化失败: {str(e)}")
        raise

if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    main() 