# coding=utf-8
import base64
import datetime
import hmac
import hashlib
import logging

import urllib

import httpx
import requests

import os
from Crypto.Cipher import AES

class Cipher:
    """
    加密工具类
    """

    def __init__(self, aes_key):
        self.aes_key = base64.b64decode(base64.b64encode(str(aes_key).encode("utf-8")))

    def encrypt(self, data: str):
        """
        加密
        :param data:
        :return:
        """
        iv = os.urandom(12)
        aes_cipher = AES.new(self.aes_key, AES.MODE_GCM, iv)
        ciphertext, auth_tag = aes_cipher.encrypt_and_digest(data.encode("utf-8"))
        result = iv + ciphertext + auth_tag
        return base64.b64encode(result).decode("utf-8")

    def decrypt(self, data):
        """
        解密
        :param data:
        :return:
        """
        res_bytes = base64.b64decode(data.encode("utf-8"))
        nonce = res_bytes[:12]
        ciphertext = res_bytes[12:-16]
        auth_tag = res_bytes[-16:]
        aes_cipher = AES.new(self.aes_key, AES.MODE_GCM, nonce)
        return aes_cipher.decrypt_and_verify(ciphertext, auth_tag).decode("utf-8")


def urlparse(urls):
    """
    将url解析为字典，{'hostname': '', 'path': ''}

    :param url:
    :return:
    """
    if urls is None:
        return {'hostname': '', 'path': ''}

    host_and_path_str = urls.split("http://")[1]
    host_and_path_dict = host_and_path_str.partition('/')

    return {'hostname': host_and_path_dict[0], 'path': '/' + host_and_path_dict[2]}


def gen_auth(access_key, secret_key, utc_time_str, url, method):
    url_parse_ret = urlparse(url)

    host = url_parse_ret['hostname']
    path = url_parse_ret['path']
    version = "1"
    expiration_seconds = "1800"
    signature_headers = "host"
    # 1 Generate SigningKey
    val = "bce-auth-v%s/%s/%s/%s" % (version, access_key, utc_time_str, expiration_seconds)
    signing_key = hmac.new(bytes(secret_key, encoding="utf-8"), val.encode("utf-8"),
                           hashlib.sha256).hexdigest().encode('utf-8')
    # 2 Generate CanonicalRequest
    # 2.1 Genrate CanonicalURI
    canonical_uri = urllib.parse.quote(path)
    # 2.2 Generate CanonicalURI: not used here
    # 2.3 Generate CanonicalHeaders: only include host here
    canonical_headers = "host:%s" % urllib.parse.quote(host).strip()

    # 2.4 Generate CanonicalRequest
    canonical_request = "%s\n%s\n\n%s" % (method.upper(), canonical_uri, canonical_headers)

    # 3 Generate Final Signature
    signature = hmac.new(signing_key, canonical_request.encode("utf-8"), hashlib.sha256).hexdigest()
    authorization = "bce-auth-v%s/%s/%s/%s/%s/%s" % (
        version, access_key, utc_time_str, expiration_seconds, signature_headers, signature)
    return authorization


async def post(payload: dict):
    # 替换ak/sk
    access_key = ""
    secret_key = ""
    cipher = Cipher(secret_key[8:24])
    # 替换url
    url = "http://***********:8579/trader_robot/order_inspect"
    method = "POST"
    utc_time = datetime.datetime.utcnow()
    utc_time_str = utc_time.strftime("%Y-%m-%dT%H:%M:%SZ")

    auth = gen_auth(access_key, secret_key, utc_time_str, url, method)

    # 替换cloudid
    header = {
        "X-Bce-Signature": auth
    }
    r = await fetch(url, payload, header)
    logging.info(f"意图识别返回内容: {r}")
    return r


async def fetch(url, jsoner, header):
    timeout = httpx.Timeout(90.0)
    async with httpx.AsyncClient(timeout=timeout) as client:
        response = await client.post(url, json=jsoner, headers=header)
        return response.json()



if __name__ == "__main__":
    # 测试代码
    test_payload = {
        "messages": [
            {
                "role": "user",
                "img_base64": "",
                "content_type": "Text",
                "content": "外贸信托粤湾15号 交易所终端申报 Z06323 出给 平安证券 Z07604 【7D】 2.2 508009 中金安徽交控REIT 360万份，6.434 85.49 19,801,535.76",
                "task_id": "",
                "refer": []
            }]
    }
    import asyncio
    result = asyncio.run(post(test_payload))
    print(result)

