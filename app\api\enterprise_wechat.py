from fastapi import APIRouter, Depends, BackgroundTasks, Request
from sqlalchemy.orm import Session
from app.external_service import e_wechat_robot
from app.assets.public_assets import general_return_json
from app.core.database import get_db
#from app.schemas.schemas import RobotActivityUpdate


#####################################
#           FBI WARNING             #
#    此为企业微信接口以及业务部分代码      #
#               现已弃用              #
#                                   #
#####################################

# router = APIRouter(prefix="/ewechat/robot")
#
# #获取所有机器人信息
# @router.get("/info")
# async def get_robot_info(db: Session=Depends(get_db)):
#     return await e_wechat.get_all_robot_info(db=db)
#
# #控制机器人消息回调 启 or 停
# @router.post("/activity")
# async def post_robot_activity(robot_parameter: RobotActivityUpdate, db: Session=Depends(get_db)):
#     return await e_wechat.modify_robot_activity(robot_parameter, db)
#
# #消息回调接口，identifier用来辨识机器人ID
# @router.post("/message/{identifier}")
# async def get_message(request: Request, identifier: str, background_tasks: BackgroundTasks, db: Session=Depends(get_db)):
#     background_tasks.add_task(e_wechat.recv_messages,await request.json(),identifier,db=db)
#     # fixed response required
#     return general_return_json(0,"参数接收成功")