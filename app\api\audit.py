#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2019 Tianxiadatong, Co.,Ltd. All Rights Reserved.

This module implements the FastAPI sub-router for the /audit endpoint,
including related business logic.

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06
"""
import json
import logging
import re
import traceback
import uuid

from fastapi import APIRouter, Request, Depends, Query, HTTPException

from sqlalchemy.orm import Session
from sqlalchemy import or_, and_

from app.assets.public_assets import general_return_json
from app.core import qq_command_processor as QQCommandProcessor, wechat_command_processor as WeChatCommandProcessor
from app.core.database import get_db
from app.decorator.auth import required_login
from app.dict import command
from app.external_service import wechat_websocket_channel, third_party, qq_websocket_channel
from app.models.models import Audit, Instruction, Task, Message, Group, GroupProduction, Production, GroupConsigner, \
    Consigner, User, GroupTrader, Robot, Log, GroupMember, BbTFundInfo, BbTTraderival, BbTTraderivalLinkman
from app.schemas.schemas import AuditRead, AuditReply, AuditBase, AuditOut
from app.oss.client import get_base64_from_obs

router = APIRouter(prefix="/audit")


def parse_number_with_unit(value):
    import re

    if not isinstance(value, str):
        return value  # 保留非字符串原值

    value = value.strip().upper()
    match = re.match(r'^([0-9.]+)([KWMB亿]?)$', value)

    if not match:
        return value  # 无法匹配，返回原值

    number_str, unit = match.groups()

    try:
        number = float(number_str)
    except ValueError:
        return value  # 数字部分非法，返回原值

    unit_multipliers = {
        '': 1,
        'K': 1_000,
        'W': 10_000,
        'M': 1_000_000,
        'B': 1_000_000_000,
        '亿': 100_000_000
    }

    multiplier = unit_multipliers.get(unit, 1)
    return number * multiplier


category_mapping = {
    1: and_(Audit.variety == '现券', Audit.market == '银行间'),
    2: and_(Audit.variety == '现券', Audit.market == '上海'),
    3: and_(Audit.variety == '现券', Audit.market == '深圳'),
    4: and_(Audit.variety == '回购', Audit.market == '银行间'),
    5: and_(Audit.variety == '回购', Audit.market == '上海'),
    6: and_(Audit.variety == '回购', Audit.market == '深圳'),
}

reverse_category_mapping = {
    "现券": {
        "银行间": 1,
        "上海": 2,
        "深圳": 3
    },
    "回购": {
        "银行间": 4,
        "上海": 5,
        "深圳": 6
    }
}

Audit_StatusPrefixAllowanceFlow = {
    0: {1, 2, 10},
    1: {0},
    10: {11},
    100: {101, 110},
    110: {101},
    1000: {1010, 1001},
    1010: {1001}
}


# 审核状态枚举 - 使用位标志设计，便于组合和判断
class AuditStatus:
    # 基础状态
    PENDING = 0  # 待处理
    INVALID = 1  # 已失效
    CONFIRMED = 2  # 已确认

    # 状态修饰符（可与基础状态组合）
    WITHDRAWN = 10  # 已撤回
    MISSING_FIELDS = 100  # 要素不全
    NO_PERMISSION = 1000  # 无权限
    MODIFIED = 10000  # 已更改


# 状态显示映射
Audit_StatusMapper = {
    # 0: "待处理",
    # 1: "已失效",
    # 2: "已确认",
    0: "",
    1: "",
    2: "",
    10: "指令已撤回",
    # 11: "已失效（已撤回）",
    # 12: "已确认（已撤回）",
    11: "指令已撤回",
    12: "指令已撤回",
    100: "要素不全",
    # 101: "已失效（要素不全）",
    101: "要素不全",
    102: "已确认（要素不全）",
    110: "要素不全/指令已撤回",
    # 111: "已失效（要素不全+已撤回）",
    111: "要素不全/指令已撤回",
    112: "已确认（要素不全+已撤回）",
    1000: "无权限",
    1001: "已失效/无权限",
    1002: "已确认（无权限）",
    1010: "无权限/指令已撤回",
    # 1011: "已失效（无权限+已撤回）",
    # 1012: "已确认（无权限+已撤回）",
    1011: "无权限/指令已撤回",
    1012: "无权限/指令已撤回",
    10000: "指令已更改",
    # 10001: "已失效（已更改）",
    # 10002: "已确认（已更改）",
    10001: "指令已更改",
    10002: "指令已更改",
    10100: "要素不全/指令已更改",
    10101: "要素不全/指令已更改",
    10102: "要素不全/指令已更改"
}

# 状态转换规则 - 定义哪些状态可以转换到哪些状态
Audit_StatusTransitions = {
    # 待处理状态可以转换的目标状态
    0: {1, 2, 10, 100, 1000, 10000},  # 待处理 -> 失效/确认/撤回/要素不全/无权限/已更改
    1: {0},  # 已失效 -> 还原到待处理
    10: {11, 12},  # 已撤回 -> 失效/确认
    11: {10},  # 已失效(已撤回) -> 还原到已撤回
    100: {101, 102, 110},  # 要素不全 -> 失效/确认/撤回
    101: {100},  # 已失效(要素不全) -> 还原
    110: {111, 112},  # 要素不全(已撤回) -> 失效/确认
    111: {110},  # 已失效(要素不全+已撤回) -> 还原
    1000: {1001, 1002, 1010},  # 无权限 -> 失效/确认/撤回
    1001: {1000},  # 已失效(无权限) -> 还原
    1010: {1011, 1012},  # 无权限(已撤回) -> 失效/确认
    1011: {1010},  # 已失效(无权限+已撤回) -> 还原
    10000: {10001, 10002},  # 已更改 -> 失效/确认
    10001: {10000},  # 已失效(已更改) -> 还原
    10100: {10101, 10110},  # 要素不全+已更改 -> 失效/撤回
    10101: {10100},  # 已失效(要素不全+已更改) -> 还原
}


# 状态判断辅助函数
def is_pending(status):
    """判断是否为待处理状态"""
    return status % 10 == 0


def is_invalid(status):
    """判断是否为已失效状态"""
    return status % 10 == 1


def is_confirmed(status):
    """判断是否为已确认状态"""
    return status % 10 == 2


def is_withdrawn(status):
    """判断是否已撤回"""
    return (status // 10) % 10 == 1


def is_missing_fields(status):
    """判断是否要素不全"""
    return (status // 100) % 10 == 1


def is_no_permission(status):
    """判断是否无权限"""
    return (status // 1000) % 10 == 1


def is_modified(status):
    """判断是否已更改"""
    return status >= 10000


def can_confirm(status):
    """判断是否可以确认"""
    return is_pending(status) and not is_missing_fields(status) and not is_no_permission(status)


def can_invalid(status):
    """判断是否可以失效"""
    return is_pending(status)


def can_restore(status):
    """判断是否可以还原"""
    # 只有已失效状态可以还原，不管有什么其他标志
    return is_invalid(status)


def can_modify(status):
    """判断是否可以编辑"""
    return status in [0, 10000, 100]  # 待处理、已更改状态可编辑


bond_map = {
    "债券买入": 3,
    "债券卖出": 4,
    "买": 3,
    "买入": 3,
    "bid": 3,
    "Bid": 3,
    "BID": 3,
    "卖": 4,
    "卖出": 4,
    "ofr": 4,
    "Ofr": 4,
    "OFR": 4,
}
repo_map = {
    "融资（正）回购": 5,
    "融资（逆）回购": 6,
    "正回购": 5,
    "正协回": 5,
    "正": 5,
    "逆回购": 6,
    "逆协回": 6,
    "逆": 6,
}
expire_map = {
    "正回购到期": 105,
    "正回购续做": 109,
    "逆回购续做": 110,
    "正回购提前到期": 113,
    "逆回购提前到期": 114,
    "正回购换券": 107,
    "逆回购换券": 108,
}
Audit_ModifyMapper = {
    1: {
        "现券买卖": bond_map
    },
    2: {
        "现券买卖": bond_map
    },
    3: {
        "现券买卖": bond_map
    },
    4: {
        "正逆回购": repo_map,
        "换券": repo_map,
    },
    5: {
        "初始交易": {
            "协议（正）回购": 103,
            "协议（逆）回购": 104,
            "正": 103,
            "正回购": 103,
            "正协回": 103,
            "逆回购": 104,
            "逆协回": 104,
            "逆": 104
        },
        "到期": {
            "正回购": 105,
            "正协回": 105,
            "正": 105
        },
        "续作": {
            "正": 109,
            "正回购": 109,
            "正协回": 109,
            "逆回购": 110,
            "逆协回": 110,
            "逆": 110
        },
        "提前到期": {
            "正": 113,
            "正回购": 113,
            "正协回": 113,
            "逆回购": 114,
            "逆协回": 114,
            "逆": 114
        },
        "换券": {
            "正": 107,
            "正回购": 107,
            "正协回": 107,
            "逆回购": 108,
            "逆协回": 108,
            "逆": 108
        },
    },
    6: {
        "初始交易": {
            "协议（正）回购": 103,
            "协议（逆）回购": 104,
            "正": 103,
            "正回购": 103,
            "正协回": 103,
            "逆回购": 104,
            "逆协回": 104,
            "逆": 104
        },
        "到期": {
            "正回购": 105,
            "正协回": 105,
            "正": 105,
            "逆回购": 106,
            "逆协回": 106,
            "逆": 106
        },
        "续作": {
            "正": 109,
            "正回购": 109,
            "正协回": 109,
            "逆回购": 110,
            "逆协回": 110,
            "逆": 110
        },
        "提前到期": {
            "正": 113,
            "正回购": 113,
            "正协回": 113,
            "逆回购": 114,
            "逆协回": 114,
            "逆": 114
        },
        "换券": {
            "正": 107,
            "正回购": 107,
            "正协回": 107,
            "逆回购": 108,
            "逆协回": 108,
            "逆": 108
        },
    }
}

# 每个 Tab 对应的必填项, 如果没有则渲染为 缺失
Audit_StatusPrefixRequiredFields = {
    # 现券（银行间）
    1: ["execution", "production_fund_caption", "production_id", "direction", "security_code", "denomination", "liquidation_speed", "rival"],
    # 现券（上海）
    2: ["execution", "production_fund_caption", "production_id", "direction", "security_code", "net_price",
        "denomination",
        "rival_trader_id", "agreement_number"],
    # 现券（深圳）
    3: ["execution", "production_fund_caption", "production_id", "direction", "security_code", "net_price",
        "denomination",
        "rival_dealer_code", 'trader_entity_code'],
    # 回购（银行间）
    4: ["execution", "production_fund_caption", "production_id", "direction", "yield_rate", "denomination",
        "liquidation_speed",
        "rival", "rival_trader", "deadline", "pledge_coupon_code", "pledge_quantity", "discount_rate"],
    # 回购（上海）
    5: ["execution", "production_fund_caption", "production_id", "direction", "three_party_sequel", "yield_rate",
        "denomination", "deadline", "pledge_coupon_code", "pledge_quantity", "discount_rate"],
    # 回购（深圳）
    6: ["execution", "production_fund_caption", "production_id", "direction", "three_party_sequel", "yield_rate",
        "denomination",
        "rival_dealer_code", "trader_entity_code", "deadline", "pledge_coupon_code",
        "pledge_quantity", "discount_rate"],
}

# 字段映射
Audit_Variable = {
    "id": "ID",
    "variety": "品种",
    "market": "市场",
    "classification": "分类",
    "execution": "执行日",
    "production": "产品名称",
    "production_fund_caption": "标准产品名称",
    "production_id": "标准产品代码",
    "direction": "方向",
    "three_party_sequel": "三方续作",
    "security_code": "证券代码",
    "security_name": "证券名",
    "yield_rate": "收益率",
    "net_price": "净价/委托价格",
    "full_price": "全价",
    "yield_to_maturity": "到期收益率",
    "option_yield": "行权收益率",
    "quantity": "数量",
    "denomination": "金额/面额",
    "liquidation_speed": "清算速度",
    "rival": "对手方",
    "rival_trader": "对手方交易员",
    "rival_seat": "对手席位",
    "rival_trader_id": "对手交易员号",
    "agreement_number": "约定号",
    "payment_method": "结算方式",
    "declaration_type": "申报类型",
    "interbank_organ_code": "对手方机构编号",
    "rival_dealer_code": "对手交易商代码",
    "trader_entity_code": "交易商主体代码",
    "rival_trader_code": "对手交易员代码",
    "deadline": "期限",
    "violation_grace_period": "违约宽限期",
    "supplementary_term": "补充条款",
    "share_bonus_method": "分红方式",
    "distribution_channel": "销售渠道",
    "pledge_coupon_code": "质押券代码",
    "pledge_coupon_name": "质押券名称",
    "pledge_quantity": "质押数量",
    "discount_rate": "折扣率",
    "message": "原始消息",
    "instruction_id": "指令ID，懂的都懂",
    "status": "未处理0/已撤回(未处理)1/已变更(未处理)2/已处理(失效)3/已处理(确认)4/已处理撤回(禁止还原)5/无权限（未处理)6/无权限已处理7",
    "auditor_id": "审核人",
    "Remark": "<UNK>",
    "o45_return_json": "O45返回值",
    "created_at": "创建时间",
    "updated_at": "更新时间"
}

# 判断一个数是否是空值
def check_e_number(v):
    if v == "":
        return False
    if v == "/":
        return False
    return True

def check_zero(v):
    if not check_e_number(v):
        return False
    if type(v) == str:
        if "%" in v:
            v = v[:-1]
    if float(v) == 0:
        return True
    return False

def check_values(data):
    net_price_flag = check_e_number(data["net_price"])
    yield_rate_flag = check_e_number(data["yield_rate"])
    warrant_yield_rate_flag = check_e_number(data["warrant_yield_rate"])
    print(net_price_flag, yield_rate_flag, warrant_yield_rate_flag)
    # 判断三个值是否都为空,如果都为空,flag为False
    flag = net_price_flag or yield_rate_flag or warrant_yield_rate_flag
    if not flag:
        return False, "净价价格、到期收益率、行权收益率，不能同时为空"
    net_price_zero = check_zero(data["net_price"])
    yield_rate_zero = check_zero(data["yield_rate"])
    warrant_yielrate_zero = check_zero(data["warrant_yield_rate"])
    print(net_price_zero, yield_rate_zero, warrant_yielrate_zero)
    zero_flag = net_price_zero and yield_rate_zero and warrant_yielrate_zero
    if zero_flag:
        return False, "净价价格、到期收益率、行权收益率，不能同时为0"
    return True, ""


@router.post("/confirm", summary="确认人工审核项。")
@required_login(roles=["salesman", "admin", "super_admin"])
async def operate(request: Request, audit: AuditRead, db: Session = Depends(get_db)):
    """确认人工审核项。

    Args:
        request (Request): FastAPI 请求对象
        audit (AuditRead): 审核项数据模型
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 确认成功
            - 404: 审核项不存在
            - 417: 审核项已被其他管理员处理

    Raises:
        HTTPException: 数据库操作异常
    """
    try:
        element = db.query(Audit).filter(Audit.id == audit.id).first()

        logging.info(f"审核确认：{element.id} , 操作者:{request.session.get('auth').get('username')}")
        user = db.get(User, request.session.get("auth").get("id"))
        logging.info(user)
        log = Log(
            operator=element.id,
            target="审核" + str(element.instruction_id),
            action="确认操作",
            result=False,
        )
        db.add(log)
        db.commit()
        if element is None:
            return general_return_json(404, "不存在该项")

        # 获取 category
        variety = str(element.variety.value) if hasattr(element.variety, 'value') else str(element.variety)
        market = str(element.market.value) if hasattr(element.market, 'value') else str(element.market)

        # logging.info(f"审核确认：[{element.id}]{variety}{market} , 操作者:{user.username}")
        category = reverse_category_mapping.get(variety, {}).get(market)
        # logging.info(f"审核确认：[{element.id}]{variety}{market} , category:{category} , 操作者:{user.username}")
        if not category:
            return general_return_json(400, "无效的品种或市场")

        required_fields = Audit_StatusPrefixRequiredFields.get(category, [])

        # 检查必填项是否缺失
        missing_fields = []
        for field in required_fields:
            field_value = getattr(element, field, None)
            if field_value == "缺失" or field_value is None or field_value == "":
                # 使用 Audit_Variable 映射获取字段的中文名称，如果不存在则使用原字段名
                field_display = Audit_Variable.get(field, field)
                missing_fields.append(field_display)

        if missing_fields:
            return general_return_json(400, f"必填项缺失: {', '.join(missing_fields)}")

        allowanceFlow = Audit_StatusPrefixAllowanceFlow.get(element.status % 10000)

        if allowanceFlow is None:
            return general_return_json(409, '此指令不允许确认')

        if element.status % 10 == 2:
            return general_return_json(409, '此指令可能已经被其他交易员确认')
        # element.status = element.status - element.status % 10 + 2
        element.auditor_id = user.id
        # log.result = True
        # db.add(log)

        logging.info(f"审核确认：[{element.id}]{element.variety}{element.market} , 操作者:{user.username}")

        if element.variety == '现券' and element.market == '银行间':
            # 现券银行间 净价价格、到期收益率、行权收益率，三者选其一 做三空校验
            data = {"net_price": element.net_price, "yield_rate": element.yield_to_maturity, "warrant_yield_rate": element.option_yield}
            result = {k: "" if v in [None, "","\\","缺失","NaN"] else v for k, v in data.items()}
            af_empty = all(v == "" for v in result.values())
            if af_empty:
                return general_return_json(409, '净价价格、到期收益率、行权收益率，三者选其一')
            # 定义检查函数
            # 检查所有值是否为 0 或 0.00
            flag, msg =  check_values(data)
            if not flag:
                return general_return_json(409, msg)
            result = await third_party.O45LocalCurrencyBusinessInstruction(element)
        if element.variety == '现券' and element.market == '上海':
            result = await third_party.O45FixedIncomePlatformInstructionForShanghai(element)
        if element.variety == '现券' and element.market == '深圳':
            result = await third_party.O45FixedIncomePlatformInstructionForShenzhen(element)
        if element.variety == '回购' and element.market == '银行间' and element.classification == '正逆回购':
            result = await third_party.O45LocalCurrencyBusinessInstructionFW(element)
        if element.variety == '回购' and (element.market == '深圳' or element.market == '上海'):
            if element.classification == '初始交易':
                result = await third_party.O45FixedIncomePlatformInstructionPledge(element)
            elif element.classification == '到期' or element.classification == '续作' or element.classification == '提前到期':
                result = await third_party.O45FixedIncomePlatformInstructionPledge2(element)
        print(result.status_code)
        if result.status_code == 200:
            print(json.loads(result.content).get("error_no"))
            if json.loads(result.content).get("error_no") == 0:
                element.status = 2
                element.o45_return_json = "成功"
                db.commit()
                return general_return_json(200, "ok")
            else:
                element.o45_return_json = json.loads(result.content).get("error_info")
                element.guid = uuid.uuid4().hex[:16]
                db.commit()
                return general_return_json(400, json.loads(result.content).get("error_info"))
        else:
            return general_return_json(400, "请求错误")


    except HTTPException as ex:
        raise ex
    except Exception as e:
        logging.error(f"审核确认操作出错：{str(e)}")
        print(traceback.format_exc())


@router.post("/invalid", summary="失效化人工审核项。")
@required_login(roles=["admin", "salesman", "super_admin"])
async def operate_invalid(request: Request, audit: AuditRead, db: Session = Depends(get_db)):
    try:
        element = db.query(Audit).filter(Audit.id == audit.id).first()
        if element is None:
            return general_return_json(404, "不存在该项")

        user = db.get(User, request.session.get("auth").get("id"))

        # 使用新的状态判断函数
        if not can_invalid(element.status):
            return general_return_json(409,
                                       f'此指令不允许失效，当前状态：{Audit_StatusMapper.get(element.status, "未知状态")}')

        # 更新状态为已失效，保留其他状态标志
        base_status = element.status - (element.status % 10)  # 移除基础状态位
        element.status = base_status + AuditStatus.INVALID
        element.auditor_id = user.id

        log = Log(
            operator=user.nickname,
            target="审核" + str(element.instruction_id),
            action="失效操作",
            object="业务审核",
            result=True,
        )
        db.add(log)
        db.commit()
        logging.info(f"审核失效：[{element.id}]{element.variety}{element.market} , 操作者:{user.username}")
        return general_return_json(200, "ok")
    except Exception as e:
        logging.error(f"审核失效操作出错：{e}")
        log = Log(
            operator=user.nickname,
            target="审核" + str(element.instruction_id),
            action="失效操作",
            object="业务审核",
            result=False,
        )
        db.add(log)
        db.commit()
        return general_return_json(500, "操作失败")
    finally:
        # 确保数据库会话被正确关闭
        db.close()


@router.post("/restore", summary="还原人工审核项。")
@required_login(roles=["admin", "salesman", "super_admin"])
async def operate(request: Request, audit: AuditRead, db: Session = Depends(get_db)):
    """还原人工审核项。

    Args:
        request (Request): FastAPI 请求对象
        audit (AuditRead): 审核项数据模型
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 还原成功
            - 404: 审核项不存在
            - 409: 不允许还原

    Raises:
        HTTPException: 数据库操作异常
    """
    try:
        element = db.query(Audit).filter(Audit.id == audit.id).first()
        if element is None:
            return general_return_json(404, "不存在该项")

        user = db.get(User, request.session.get("auth").get("id"))
        log = Log(
            operator=user.nickname,
            target="审核" + str(element.instruction_id),
            action="还原操作",
            result=False,
        )

        # 使用新的状态判断函数 - 只要不是确认状态都可以还原
        if is_confirmed(element.status):
            return general_return_json(409,
                                       f'此指令不允许还原，当前状态：{Audit_StatusMapper.get(element.status, "未知状态")}')

        # 还原到待处理状态，保留其他状态标志
        base_status = element.status - (element.status % 10)  # 移除基础状态位
        element.status = base_status + AuditStatus.PENDING

        log.result = True
        db.add(log)
        db.commit()
        logging.info(f"审核还原：[{element.id}]{element.variety}{element.market} , 操作者:{user.username}")
        return general_return_json(200, "ok")
    except HTTPException as ex:
        raise ex
    except Exception as e:
        logging.error(f"审核还原操作出错：{e}")
        return general_return_json(500, "操作失败")
    finally:
        # 确保数据库会话被正确关闭
        db.close()


async def process_message_for_image(message):
    logging.info(f"处理消息内容: {message.content}")
    logging.info(f"消息类型: {message.type}")
    """处理消息，如果是图片则获取base64数据"""
    if message.type >= 2:  # 图片消息
        try:
            status, base64_data = await get_base64_from_obs(message.content)
            logging.info(f"获取图片base64状态: {status}, 数据长度: {len(base64_data) if base64_data else '无数据'}")
            if status:
                # 根据文件扩展名确定MIME类型
                if message.content.lower().endswith(('.jpg', '.jpeg')):
                    mime_type = "image/jpeg"
                elif message.content.lower().endswith('.png'):
                    mime_type = "image/png"
                elif message.content.lower().endswith('.gif'):
                    mime_type = "image/gif"
                else:
                    mime_type = "image/jpeg"

                # data_url = f"data:{mime_type};base64,{base64_data}"

                return {
                    "original_content": message.content,
                    "base64": base64_data,
                    # "data_url": data_url,
                    "is_image": True
                }
            else:
                logging.error(f"获取图片base64失败: {base64_data}")
                return {
                    "original_content": message.content,
                    "is_image": True,
                    "error": base64_data
                }
        except Exception as e:
            logging.error(f"处理图片消息出错: {str(e)}")
            return {
                "original_content": message.content,
                "is_image": True,
                "error": str(e)
            }
    else:
        return {
            "original_content": message.content,
            "is_image": False
        }


@router.get("/", summary="获取单条审核记录的完整详情")
@required_login(roles=["admin", "salesman", "super_admin"])
async def get_single(request: Request, audit_id: int, db: Session = Depends(get_db)):
    """获取单条审核记录的完整详情。"""
    try:
        audit = db.get(Audit, audit_id)
        if not audit:
            return general_return_json(404, "审核记录不存在")

        instruction = db.query(Instruction).filter_by(id=audit.instruction_id).first()
        if not instruction:
            return general_return_json(404, "指令不存在")

        task = db.query(Task).filter_by(id=instruction.task_id).first()
        if not task:
            return general_return_json(404, "任务不存在")

        message = db.query(Message).filter_by(id=task.message_id).first()
        if not message:
            return general_return_json(404, "原始消息不存在")

        # 处理原始消息的图片内容
        origin_processed = await process_message_for_image(message)

        # 获取所有回复该消息的内容并处理图片
        replies = db.query(Message).filter(Message.reply == message.id).all()
        processed_replies = []
        for reply in replies:
            reply_processed = await process_message_for_image(reply)
            # 将处理后的内容添加到reply对象中
            reply_dict = reply.__dict__.copy()
            reply_dict['processed_content'] = reply_processed
            processed_replies.append(reply_dict)

        group = db.query(Group).filter(
            or_(
                Group.number == message.position,
                Group.name == message.position
            )
        ).first()

        if group:
            group_productions = db.query(GroupProduction).filter_by(group_id=str(group.id)).filter(
                GroupProduction.is_deleted == False).all()
            production_ids = [str(gp.production_id) for gp in group_productions]
            prod_elements = db.query(BbTFundInfo.fund_name).filter(BbTFundInfo.fund_code.in_(production_ids)).all()
            prods = [p[0] for p in prod_elements]

            group_consigners = db.query(GroupConsigner).filter_by(group_id=str(group.id)).filter(
                GroupConsigner.is_deleted == False).all()
            consigner_ids = [gc.consigner_id for gc in group_consigners]
            cons = db.query(Consigner).filter(Consigner.id.in_(consigner_ids)).all()

            group_traders = db.query(GroupTrader).filter_by(group_id=group.id).filter(
                GroupTrader.is_deleted == False).all()
            trader_ids = [str(gt.trader_id) for gt in group_traders]
            trader_elements = db.query(GroupMember.name).filter_by(group_id=str(group.id)).filter(
                GroupMember.id.in_(trader_ids)).all()
            traders = [p[0] for p in trader_elements]
        else:
            prods = []
            cons = []
            traders = []

        return general_return_json(200, "ok", {
            "audit": audit,
            "hint": f"{Audit_StatusMapper.get(audit.status)}",
            "origin": {
                **message.__dict__,
                "processed_content": origin_processed
            },
            "replies": processed_replies,
            "group": {
                "data": group,
                "product": prods,
                "consigner": cons,
                "trader": traders
            }
        })
    except HTTPException as ex:
        raise ex
    except Exception as e:
        logging.error(f"获取单条AUDIT出错：{e}")
        print(traceback.format_exc())
    finally:
        db.close()


async def get_audit_data(db: Session, status_list: list, category: int, page: int = 1, page_size: int = 10,
                         is_review: bool = False):
    """
    获取审核数据的通用函数

    Args:
        db: 数据库会话
        status_list: 状态列表
        category: 分类ID
        page: 页码
        page_size: 每页记录数

    Returns:
        dict: 审核数据
    """
    try:
        offset = (page - 1) * page_size

        category_filter = category_mapping.get(category)
        if category_filter is None:
            return None

        # 基础查询构建
        query = (
            db.query(Audit, Instruction, Task, Message)
            .join(Instruction, Audit.instruction_id == Instruction.id)
            .join(Task, Instruction.task_id == Task.id)
            .join(Message, Task.message_id == Message.id)
            .filter(Audit.status.in_(status_list))
            .filter(category_filter)
        )

        # 根据 is_review 决定排序方式
        if is_review:
            query = query.order_by(Audit.updated_at)
        else:
            query = query.order_by(Audit.created_at)

        # 最后应用分页
        query = query.offset(offset).limit(page_size)

        # 执行查询
        audits = query.all()

        # logging.info(len(audits))
        result = []
        # 获取该分类的必填字段
        required_fields = Audit_StatusPrefixRequiredFields.get(category, [])

        for audit, instruction, task, message in audits:
            # 处理必填字段
            audit_dict = audit.__dict__
            for field in required_fields:
                if field in audit_dict and (audit_dict[field] is None or audit_dict[field] == ''):
                    setattr(audit, field, "缺失")

            # 获取所有回复当前 message 的消息
            replies = db.query(Message).filter(Message.reply == message.id).all()

            # 获取 Group 信息
            group = db.query(Group).filter(
                or_(
                    Group.number == message.position,
                    Group.name == message.position
                )
            ).first()

            group_data = {"data": None, "product": [], "consigner": [], "trader": []}
            if group:
                group_data["data"] = group

                # 产品信息
                group_productions = db.query(GroupProduction).filter_by(group_id=group.id).all()
                if group_productions:
                    production_ids = [gp.production_id for gp in group_productions]
                    group_data["product"] = db.query(Production).filter(Production.id.in_(production_ids)).all()

                # 托运人信息
                group_consigners = db.query(GroupConsigner).filter_by(group_id=group.id).all()
                if group_consigners:
                    consigner_ids = [gc.consigner_id for gc in group_consigners]
                    group_data["consigner"] = db.query(Consigner).filter(Consigner.id.in_(consigner_ids)).all()

                # 交易员信息
                group_traders = db.query(GroupTrader).filter_by(group_id=group.id).all()
                if group_traders:
                    trader_ids = [gt.trader_id for gt in group_traders]
                    group_data["trader"] = db.query(User).filter(User.id.in_(trader_ids)).all()

            result.append({
                "audit": audit,
                "hint": f"{Audit_StatusMapper.get(audit.status)}",
                "task": task.id,
                # "origin": {
                #     "sender": message.sender,
                #     "receiver": message.receiver,
                #     "position": message.position,
                #     "reply": message.reply,
                #     "date": message.date,
                #     "role": message.role,
                #     "content": message.content,
                #     "id": message.id,
                #     "type": message.type,
                #     "sequence": message.sequence,
                #     "channel": message.channel,
                #     # 明确不包含 result
                # },
                "replies": replies,
                "group": group_data,
                "invalidable": audit.status % 10 == 0,
                "confirmable": audit.status in (0, 10000),
                "rewindable": not is_confirmed(audit.status),
                "modifiable": can_modify(audit.status),
            })

        # 总数统计
        total = (
            db.query(Audit)
            .filter(Audit.status.in_(status_list))
            .filter(category_filter)
            .count()
        )
        return {
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": (total + page_size - 1) // page_size,
            "data": result
        }
    except HTTPException as ex:
        raise ex
    except Exception as e:
        logging.error(f"获取审核数据出错：{e}")
        print(traceback.format_exc())
    finally:
        # 确保数据库会话被正确关闭
        db.close()


# 定义状态常量，便于维护
PENDING_STATUSES = [0, 10, 100, 110, 1000, 1010, 10000, 10100, 10110]  # 所有待处理状态
PROCESSED_STATUSES = [1, 2, 11, 12, 101, 102, 111, 112, 1001, 1002, 1011, 1012, 10001, 10002, 10101, 10102, 10111,
                      10112]  # 所有已处理状态


@router.get("/unchecked", summary="获取所有未处理的人工审核的item")
@required_login(roles=["admin", "salesman", "super_admin"])
async def get_unchecked(
        request: Request,
        db: Session = Depends(get_db),
        page: int = Query(1, ge=1, description="页码，从1开始"),
        page_size: int = Query(10, ge=1, le=100, description="每页记录数"),
        category: int = Query(1, ge=1, le=6, description="分类ID")
):
    try:
        result = await get_audit_data(db, PENDING_STATUSES, category, page, page_size)

        if result is None:
            return general_return_json(400, "无效的分类ID")

        return general_return_json(200, "ok", result)
    except HTTPException as ex:
        raise ex
    except Exception as e:
        logging.error(f"获取未审核记录出错：{e}")
        print(traceback.format_exc())
    finally:
        # 确保数据库会话被正确关闭
        db.close()


@router.get("/checked", summary="获取所有已处理的人工审核的item")
@required_login(roles=["admin", "salesman", "super_admin"])
async def get_checked(
        request: Request,
        db: Session = Depends(get_db),
        page: int = Query(1, ge=1, description="页码，从1开始"),
        page_size: int = Query(10, ge=1, le=100, description="每页记录数"),
        category: int = Query(1, ge=1, le=6, description="分类ID")
):
    try:
        result = await get_audit_data(db, PROCESSED_STATUSES, category, page, page_size, is_review=True)

        if result is None:
            return general_return_json(400, "无效的分类ID")

        return general_return_json(200, "ok", result)
    except HTTPException as ex:
        raise ex
    except Exception as e:
        logging.error(f"获取已审核记录出错：{e}")
        print(traceback.format_exc())
    finally:
        # 确保数据库会话被正确关闭
        db.close()


@router.post("/reply", summary="reply")
@required_login(roles=["admin", "salesman", "super_admin"])
async def audit_reply(request: Request, audit: AuditReply, db: Session = Depends(get_db)):
    """回复审核项。

    Args:
        request (Request): FastAPI 请求对象
        audit (AuditReply): 审核回复数据模型
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 回复成功
            - 400: 指令已被提交

    Raises:
        HTTPException: 数据库操作异常
    """
    try:
        audit_item = db.query(Audit).filter(Audit.id == audit.id).first()

        user = db.get(User, request.session.get("auth").get("id"))
        log = Log(
            operator=user.nickname,
            target="审核" + str(audit_item.instruction_id),
            action="回复操作",
            object="业务审核",
            result=False,
        )

        if audit_item is None:
            log.clause = "指令已被提交"
            db.add(log)
            db.commit()
            return general_return_json(400, "该指令可能已被提交")
        origin_instruct = db.query(Instruction).filter_by(id=audit_item.instruction_id).first()
        origin_task = db.query(Task).filter_by(id=origin_instruct.task_id).first()
        origin_message = db.query(Message).filter(Message.id == origin_task.message_id).first()

        print("origin_message:", origin_message.channel, origin_message.sender, origin_message.content)

        robot = db.query(Robot).filter(Robot.account == origin_message.channel).first()

        user = db.get(User, request.session.get("auth", {}).get("id", -1))
        print("user", user.nickname if user else "None")

        built_message = Message(
            sender=str(origin_message.channel),
            receiver=origin_message.sender,
            content=audit.content,
            position=origin_message.position,
            type=3,
            reply=origin_message.id,
            sequence=origin_message.sequence,
            channel=origin_message.channel
        )
        db.add(built_message)

        if robot.type == 'QQ':
            if not built_message.channel in qq_websocket_channel.active_connections:
                db.rollback()
                return general_return_json(500, "机器人不在线")
            else:
                await QQCommandProcessor.processAuditReply(built_message)
        if robot.type == '微信':
            if not built_message.channel in wechat_websocket_channel.active_connections:
                db.rollback()
                return general_return_json(500, "机器人不在线")
            else:
                await WeChatCommandProcessor.processAuditReply(built_message)

        log.result = True
        db.add(log)
        db.commit()
        logging.info(
            f"人工回复信息到：[{audit_item.id}]{audit_item.variety}{audit_item.market}, 操作者:{user.username}, 内容:{audit.content}")
        return general_return_json(200, "ok", {})
    except HTTPException as ex:
        raise ex
    except Exception as e:
        logging.error(f"人工回复出错：{e}")
        print(traceback.format_exc())
    finally:
        # 确保数据库会话被正确关闭
        db.close()


@router.post("/edit", summary="编辑")
@required_login(roles=["admin", "salesman", "super_admin"])
async def audit_edit(request: Request, audit: AuditOut, db: Session = Depends(get_db)):
    try:

        # 输出 post 请求的参数
        logging.info(f"编辑审核项请求参数：{audit.dict()}")
        count_codes = len(audit.pledge_coupon_code.split('、')) if audit.pledge_coupon_code else 0
        count_names = len(audit.pledge_coupon_name.split('、')) if audit.pledge_coupon_name else 0
        count_quantity = len(audit.pledge_quantity.split('、')) if audit.pledge_quantity else 0
        count_discount = len(audit.discount_rate.split('、')) if audit.discount_rate else 0

        if audit.pledge_coupon_code != "None":
            if len({count_codes, count_names, count_quantity, count_discount}) != 1:
                return general_return_json(400, "质押券码，质押券名，数量和折扣率字段数量不一致")

        a = db.get(Audit, audit.id)

        if not a:
            return general_return_json(404, "记录不存在")

        for key, value in audit.dict(exclude={"id"}).items():
            setattr(a, key, value)

        logging.info(f"编辑审核项：{audit.production_fund_caption}")

        # 处理产品信息
        if audit.production_fund_caption and audit.production_fund_caption != "缺失":
            product = db.query(BbTFundInfo).filter(
                BbTFundInfo.fund_name == audit.production_fund_caption
            ).first()  # 只查询一次

            if not product:
                return general_return_json(404, "产品信息不存在")

            a.production_id = product.fund_code
            a.production = product.fund_name

        # 处理对手方信息
        if audit.rival and audit.rival != "缺失":
            rival = db.query(BbTTraderival).filter(
                BbTTraderival.traderival_name == audit.rival
            ).first()

            if not rival:
                return general_return_json(404, "对手方信息不存在")

            a.rival_interbank_organ_code = rival.interbank_organ_code
            # a.trade_rival_code = rival.rival_code
            a.rival_code = rival.rival_code

        if audit.rival_trader_id and audit.rival_trader_id != "缺失":
            try:
                int(audit.rival_trader_id)
            except ValueError:
                return general_return_json(400, "找不到对手交易员")



        # if audit.rival_trader_id and audit.rival_trader_id != "缺失":
        #     trader = db.query(BbTTraderivalLinkman).filter(
        #         BbTTraderivalLinkman.contact_id == audit.rival_trader_id
        #     ).first()
        #
        #     if not trader:
        #         return general_return_json(404, "对手交易员信息不存在")
        #
        #     a.rival_trader_code = trader.rival_tradercode
        #     a.rival_trader = trader.linkman
        #     a.rival_trader_id = trader.contact_id
        #
        #     logging.info(f"编辑审核项：{audit.rival_trader}，对手交易员ID：{a.rival_trader_id}")

        activeTabBaRival = {
            "spot-interbank": "rival_trader_id",
            "spot-shanghai": "rival_trader_id",
            "spot-shenzhen": "rival_trader_id",
            "repo-interbank": "rival_trader_id",
            "repo-shanghai": "rival_trader_id",
            "repo-shenzhen": "rival_trader_id",
        }

        type = activeTabBaRival[audit.pageTap]
        change_rival = getattr(audit, str(type), None)
        if change_rival and change_rival != "缺失":
            trader = db.query(BbTTraderivalLinkman).filter(
                BbTTraderivalLinkman.contact_id == change_rival
            ).first()
            if not trader:
                return general_return_json(404, "对手交易员信息不存在")
            a.rival_trader_code = trader.rival_tradercode
            a.rival_trader = trader.linkman
            a.rival_trader_id = trader.contact_id
            logging.info("对手交易员信息存在")


        a.status = 0

        db.commit()
        return general_return_json(200, "ok")
    except Exception as ex:
        db.rollback()
        logging.error(f"编辑审核项出错：{str(ex)}")
        return general_return_json(500, str(ex))
    finally:
        # 确保数据库会话被正确关闭
        db.close()


@router.get("/production", summary="<UNK>")
@required_login(roles=["admin", "salesman", "super_admin"])
async def audit_production(request: Request, group_id: str, db: Session = Depends(get_db), page: int = Query(1),
                           size: int = Query(10), keyword: str = Query('')):
    try:
        if not group_id:
            return general_return_json(400, "Must Provide Group ID")

        query = db.query(BbTFundInfo.fund_code, BbTFundInfo.fund_name).join(GroupProduction,
                                                                            GroupProduction.production_id == BbTFundInfo.fund_code).filter(
            GroupProduction.group_id == group_id)

        # ✅ 仅当 keyword 非空时才做模糊查询
        if keyword.strip():
            like_pattern = f"%{keyword.strip()}%"
            query = query.filter(
                or_(
                    BbTFundInfo.fund_name.ilike(like_pattern),
                    BbTFundInfo.fund_code.ilike(like_pattern)
                )
            )

        total = query.count()
        prods = query.offset((page - 1) * size).limit(size).all()

        data = [
            {"fund_code": p[0], "fund_name": p[1]} for p in prods
        ]

        return general_return_json(
            200,
            "ok",
            {
                "total": total,
                "page": page,
                "size": size,
                "data": data
            }
        )

    except HTTPException as ex:
        raise ex
    except Exception as e:
        logging.error(f"获取产品出错：{e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")
    finally:
        # 确保数据库会话被正确关闭
        db.close()


@router.get("/standardrival", summary="<UNK>")
@required_login(roles=["admin", "salesman", "super_admin"])
async def audit_standard_rival(request: Request, db: Session = Depends(get_db), page: int = Query(1),
                               size: int = Query(10), keyword: str = Query('')):
    try:
        query = db.query(BbTTraderival.rival_code, BbTTraderival.traderival_name)

        # ✅ 仅当 keyword 非空时才做模糊查询
        if keyword.strip():
            like_pattern = f"%{keyword.strip()}%"
            query = query.filter(
                or_(
                    BbTTraderival.rival_code.ilike(like_pattern),
                    BbTTraderival.traderival_name.ilike(like_pattern),
                    BbTTraderival.rival_fullname.ilike(like_pattern)
                )
            ).order_by("rival_code")

        # 如果 category == 1 现卷银行间只筛选 bb_ttraderival.org_main_flag = 1
        if request.query_params.get("category") == "1" or request.query_params.get("category") == 1:
            query = query.filter(BbTTraderival.org_main_flag == 1)

        total = query.count()
        prods = query.offset((page - 1) * size).limit(size).all()

        data = [
            {"rival_code": p[0], "rival_full_name": p[1]} for p in prods
        ]

        return general_return_json(
            200,
            "ok",
            {
                "total": total,
                "page": page,
                "size": size,
                "data": data
            }
        )
    except HTTPException as ex:
        raise ex
    except Exception as e:
        logging.error(f"获取产品出错：{e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")
    finally:
        # 确保数据库会话被正确关闭
        db.close()


@router.get("/standardrival/trader", summary="<UNK>")
@required_login(roles=["admin", "salesman", "super_admin"])
async def audit_standard_rival_trader(request: Request, db: Session = Depends(get_db), page: int = Query(1),
                                      size: int = Query(10), keyword: str = Query('')):
    try:
        query = db.query(BbTTraderivalLinkman.rival_tradercode, BbTTraderivalLinkman.linkman)

        # ✅ 仅当 keyword 非空时才做模糊查询
        if keyword.strip():
            like_pattern = f"%{keyword.strip()}%"
            query = query.filter(
                or_(
                    BbTTraderivalLinkman.rival_tradercode.ilike(like_pattern),
                    BbTTraderivalLinkman.linkman.ilike(like_pattern)
                )
            )

        total = query.count()
        prods = query.offset((page - 1) * size).limit(size).all()

        data = [
            {"rivaltrader_code": p[0], "rivaltrader_name": p[1]} for p in prods
        ]

        return general_return_json(
            200,
            "ok",
            {
                "total": total,
                "page": page,
                "size": size,
                "data": data
            }
        )
    except HTTPException as ex:
        raise ex
    except Exception as e:
        logging.error(f"获取产品出错：{e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")
    finally:
        # 确保数据库会话被正确关闭
        db.close()


@router.get("/rival/traders", summary="根据对手方名称获取对手交易员列表")
@required_login(roles=["admin", "salesman", "super_admin"])
async def get_rival_traders(
        request: Request,
        rival_name: str = Query(..., description="对手方名称"),
        db: Session = Depends(get_db)
):
    """根据对手方名称获取所有对手交易员。

    Args:
        request (Request): FastAPI 请求对象
        rival_name (str): 对手方名称
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 返回对手交易员列表
            - 404: 对手方不存在或无交易员
    """
    try:
        # 先根据对手方名称查询对手方信息
        rival = db.query(BbTTraderival).filter(
            BbTTraderival.traderival_name == rival_name
        ).first()

        if not rival:
            return general_return_json(404, "未找到该对手方")

        logging.info(f"查询对手方交易员：{rival_name}, 对手ID: {rival.rival_id}")

        # 根据rival_id查询对手交易员
        traders = db.query(BbTTraderivalLinkman).filter(
            BbTTraderivalLinkman.rival_id == rival.rival_id
        ).all()

        if not traders:
            return general_return_json(404, "未找到该对手方的交易员")

        # 格式化返回数据
        trader_data = [
            {
                "contact_id": trader.contact_id,
                "linkman": trader.linkman,
                "rival_tradercode": trader.rival_tradercode,
                "phone_no": trader.phone_no,
                "email": trader.email,
                "rival_id": trader.rival_id
            }
            for trader in traders
        ]

        return general_return_json(200, "ok", trader_data)

    except Exception as e:
        logging.error(f"获取对手交易员列表出错：{e}")
        return general_return_json(500, f"获取失败: {str(e)}")
    finally:
        db.close()
