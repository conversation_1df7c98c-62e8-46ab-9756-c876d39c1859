#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2019 Tianxiadatong, Co.,Ltd. All Rights Reserved.

This module defines a standardized JSON response structure for API endpoints.

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06
"""

#常用公共方法
from typing import Optional, Any

from fastapi.encoders import jsonable_encoder


#设计通用响应 JSON
#
#   {
#       code : 业务响应代码，example : 200 代表成功 ..
#       message : 返回一些信息
#       data : 返回数据，可选
#   }
#


def general_return_json(code: int, message: str, data: Optional[Any]=None):
    return {
        "code": code,
        "message": message,
        "data": jsonable_encoder(data) if data is not None else None
    }