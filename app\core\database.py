#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2019 Tianxiadatong, Co.,Ltd. All Rights Reserved.

This module defines a database core connection and session.
It contains MySQL and PostageSQL code both.
Using one after Annotating another first.

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06
"""

import pytz
from sqlalchemy import create_engine, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.dialects.postgresql.base import PGDialect

from config import DatabaseConfig

# 设置北京时区
BEIJING_TZ = pytz.timezone('Asia/Shanghai')

@event.listens_for(PGDialect, 'do_connect')
def receive_do_connect(dialect, conn_rec, cargs, cparams):
    # 设置固定的版本信息
    dialect.server_version_info = DatabaseConfig.SERVER_VERSION_INFO
    # 覆盖版本检测方法
    dialect._get_server_version_info = lambda conn: DatabaseConfig.SERVER_VERSION_INFO
    # 禁用某些可能不兼容的 PostgreSQL 特性
    dialect.supports_sane_rowcount = DatabaseConfig.SUPPORTS_SANE_ROWCOUNT
    dialect.supports_sane_multi_rowcount = DatabaseConfig.SUPPORTS_SANE_MULTI_ROWCOUNT
    return None

engine = create_engine(
    DatabaseConfig.DATABASE_URL,
    echo=DatabaseConfig.ECHO,
    pool_pre_ping=DatabaseConfig.POOL_PRE_PING,
    connect_args={
        "application_name": DatabaseConfig.APPLICATION_NAME
    }
)

@event.listens_for(engine, 'connect')
def receive_connect(dbapi_connection, connection_record):
    # 设置会话参数
    cursor = dbapi_connection.cursor()
    cursor.execute("SET SESSION CHARACTERISTICS AS TRANSACTION ISOLATION LEVEL READ COMMITTED")
    # 设置时区为北京时间
    cursor.execute("SET timezone TO 'Asia/Shanghai'")
    cursor.close()

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

def get_db():
    """获取数据库会话。

    这是一个生成器函数，用于创建和管理数据库会话。
    它确保在使用完毕后正确关闭数据库连接。

    Yields:
        Session: SQLAlchemy 会话对象

    Example:
        db = next(get_db())
        try:
            # 使用数据库会话
            ...
        finally:
            db.close()
    """
    db: Session = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_db(drop_all: bool = False):
    """初始化数据库。
    
    此函数用于初始化数据库表结构。在开发环境中可以选择是否删除现有表。
    
    Args:
        drop_all (bool): 是否删除所有现有表。默认为False。
        
    Warning:
        在生产环境中请谨慎使用drop_all=True，这将删除所有数据！
        
    Raises:
        RuntimeError: 当在生产环境中尝试使用drop_all=True时抛出。
    """
    if drop_all and DatabaseConfig.IS_PRODUCTION:
        raise RuntimeError("禁止在生产环境中使用drop_all=True！这可能会导致数据丢失。")
        
    if drop_all:
        Base.metadata.drop_all(bind=engine)
        
    Base.metadata.create_all(bind=engine)

def get_current_time():
    """获取当前北京时间。
    
    Returns:
        datetime: 当前的北京时间
    """
    from datetime import datetime
    return datetime.now(BEIJING_TZ)
