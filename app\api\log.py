#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2019 Tianxiadatong, Co.,Ltd. All Rights Reserved.

This module implements the FastAPI sub-router for the /log endpoint,
including related business logic.

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06
"""
import logging

from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from fastapi import Request
from typing import Optional
from datetime import datetime

from app.assets.public_assets import general_return_json
from app.core.database import get_db
from app.decorator.auth import required_login
from app.models.models import Log

router = APIRouter(prefix="/log")

# 获取所有日志
@router.get("/", summary="获取所有日志")
@required_login()
async def get_all_log(
    request: Request,
    db: Session = Depends(get_db),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    target: Optional[str] = Query(None, description="操作对象（支持模糊查询）"),
    result: Optional[int] = Query(None, ge=0, le=1, description="执行结果：0-失败，1-成功"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    object: str = Query("", description="<UNK>"),
):
    """获取系统日志列表，支持多种过滤条件和分页。

    Args:
        request (Request): FastAPI 请求对象
        db (Session): 数据库会话对象
        start_time (datetime, optional): 开始时间过滤
        end_time (datetime, optional): 结束时间过滤
        target (str, optional): 操作对象关键词过滤（支持模糊查询）
        result (int, optional): 执行结果过滤（0-失败，1-成功）
        page (int): 页码，从1开始
        page_size (int): 每页记录数，范围1-100

    Returns:
        general_return_json: 返回结果
            - 200: 返回日志列表，包含分页信息
                - total: 总记录数
                - page: 当前页码
                - page_size: 每页记录数
                - total_pages: 总页数
                - data: 日志记录列表

    Raises:
        HTTPException: 数据库操作异常
        :param page_size:
        :param page:
        :param target:
        :param result:
        :param start_time:
        :param end_time:
        :param request:
        :param db:
        :param object:
    """
    # 构建查询
    try:
        query = db.query(Log)

        logging.info(f"获取日志LogTable, start_time: {start_time}, end_time: {end_time}, target: {target}, result: {result}, page: {page}, page_size: {page_size}, object: {object}")

        # 添加过滤条件
        if start_time:
            query = query.filter(Log.date >= start_time)
        if end_time:
            query = query.filter(Log.date <= end_time)
        if target:
            query = query.filter(Log.target.like(f"%{target}%"))
        if object:
            query = query.filter(Log.object == object)

        if result is not None:  # 使用 is not None 因为 0 是有效值
            query = query.filter(Log.result == result)

        # 按时间倒序排序
        query = query.order_by(Log.date.desc())

        # 获取总数
        total = query.count()

        # 分页
        logs = query.offset((page - 1) * page_size).limit(page_size).all()

        # 构建返回数据
        return general_return_json(
            200,
            "ok",
            {
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size,
                "data": logs
            }
        )
    except Exception as e:

        logging.error(f"获取日志LogTable出错: {str(e)}")
        return general_return_json(500, f"获取失败: {str(e)}")