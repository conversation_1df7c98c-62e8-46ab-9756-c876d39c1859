#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2019 Tianxiadatong, Co., Ltd. All Rights Reserved.

This main module defines FastAPI router and middlewares.

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06
"""
import logging
import sys
from contextlib import asynccontextmanager

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from starlette.middleware.sessions import SessionMiddleware

from app.api import user, robot, script, group, audit, log, template, back_ground, consigner, message, migration
from app.core.database import get_db, init_db
from app.corn import mission
from app.external_service import qq_websocket_channel, wechat_websocket_channel
from app.models.models import Robot
from config import SessionConfig, ServerConfig, SentryConfig
from app.services.migration_service import migration_service

import sentry_sdk

sentry_sdk.init(SentryConfig.URL)

sys.stdout.reconfigure(encoding='utf-8')
sys.stderr.reconfigure(encoding='utf-8')

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

async def scheduled_migration_task():
    """定时迁移任务包装函数"""
    try:
        logging.info("定时迁移任务开始执行")
        result = await migration_service.run_all_migrations_async("scheduler")
        logging.info(f"定时迁移任务完成: {result.get('job_id', 'unknown')}")
    except Exception as e:
        logging.error(f"定时迁移任务异常: {str(e)}")

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 应用启动时执行
    # 初始化数据库表结构
    try:
        logging.info("开始初始化数据库表结构...")
        init_db(False)
        logging.info("数据库表结构初始化完成")
    except Exception as e:
        logging.error(f"数据库表结构初始化失败: {str(e)}")
    
    scheduler.add_job(mission.job_syn_group_member, 'interval', minutes=10, id="job_syn_group_member")
    
    # 添加每日数据迁移任务 - 每天凌晨2点执行
    scheduler.add_job(
        scheduled_migration_task,
        'cron', 
        hour=2, 
        minute=0,
        id="daily_migration_job"
    )
    
    scheduler.start()
    logging.info("调度器SCHEDULER启动")
    logging.info("每日数据迁移任务已添加到调度器")

    #启动时应将所有机器人运行状态置为离线
    db_gen = get_db()
    db: Session = next(db_gen)
    try:
        db.query(Robot).filter(Robot.status == True).update({Robot.status:False})
        db.commit()

    except Exception as e:
        logging.warning(f"启动前未能将所有机器人运行状态置为离线: {str(e)}")

    yield  # 应用运行的上下文

    # 应用关闭时执行
    scheduler.shutdown()
    logging.info("调度器SCHEDULER关闭")
app = FastAPI(title="robot",lifespan=lifespan)

# 先添加 CORS 中间件（对 WebSocket 更友好）
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://127.0.0.1",
        "http://localhost",
        "http://localhost:80",
        "http://localhost:3000",  # 开发环境
        "http://trade_fronted",   # Docker 容器名
        "http://trade_fronted:80", # Docker 容器名带端口
        "*"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 先注册 WebSocket 路由（避免被 SessionMiddleware 影响）
app.include_router(wechat_websocket_channel.router)
app.include_router(qq_websocket_channel.router)

# 再添加 SessionMiddleware（只影响 HTTP 路由）
app.add_middleware(
    SessionMiddleware,
    secret_key=SessionConfig.SECRET_KEY,
    session_cookie=SessionConfig.SESSION_COOKIE
)

# 注册其他 HTTP 路由
#此定义企业微信接口路由已弃用
#app.include_router(enterprise_wechat.router)
app.include_router(user.router)
app.include_router(robot.router)
app.include_router(script.router)
app.include_router(group.router)
app.include_router(audit.router)
app.include_router(log.router)
app.include_router(template.router)
app.include_router(back_ground.router)
app.include_router(consigner.router)
app.include_router(migration.router)

app.include_router(message.router)
scheduler = AsyncIOScheduler()

logging.info("你好，世界 🌏✨")
print("你好，世界 🌏✨")



if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=ServerConfig.HOST,
        port=ServerConfig.PORT,
        reload=ServerConfig.RELOAD
    )
