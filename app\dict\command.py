#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2019 Tianxiadatong, Co., Ltd. All Rights Reserved.

This module defines various payloads and mapping functions
used to standardize commands between this backend and Robot backend.

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06
"""

import asyncio
import logging

from app.core import qq_command_processor as QQCommandProcessor, wechat_command_processor as WeChatCommandProcessor
from app.dict.schema import MessagePacket


def SendPrivateMessage(receiver:str,content:str):
    return {
        "command": "SendPrivateMessage",
        "message": {
            "receiver": receiver,
            "content": content
        }
    }

def AtSendWIthReplyPayload(content:MessagePacket):
    return {
        "command":"ReplyAndAt",
        "message":content.model_dump()
    }

def AtSendPayload(content:MessagePacket):
    return {
        "command":"atSend",
        "message":content.model_dump()
    }

def RogerMessagePayload(content:MessagePacket):
    return {
        "command":"RegularReply",
        "message":content.model_dump()
    }

def synGroupMemberPayload(group_id:str):
    return {
        "command":"syncGroupMember",
        "message":{
            "group_id":group_id
        }
    }



def MessageAcknowledgement(pos:str,seq:str):
    return {
        "command":"MsgAck",
        "message":{
            "sequence":seq,
            "position":pos
        }
    }

async def resolveAndExecuteQQCommand(json:dict,robot_id:str):
    RemoteCommandMapping = {
        "message-received": QQCommandProcessor.processReceivedQQ,
        "group-member-increase": QQCommandProcessor.processGroupMemberIncreasedQQ,
        "message-withdraw": QQCommandProcessor.processMessageWithdrawnQQ,
        "group-member-syn": QQCommandProcessor.processGroupMemberUpdateQQ,
        "group-member-decease": QQCommandProcessor.processGroupMemberDecreasedQQ,
        "offline":QQCommandProcessor.processOfflineQQ,
        "online":QQCommandProcessor.processOnlineQQ,
        "private-message-received":QQCommandProcessor.processPrivateMessageQQ,
    }
    r = RemoteCommandMapping.get(json.get("command"))
    if r:
        logging.info(f"接收到 {robot_id} 的命令 {json}")
        asyncio.create_task(r(json.get("message"), robot_id))

async def resolveAndExecuteWechatCommand(json:dict,robot_id:str):
    RemoteCommandMapping = {
        "message-received": WeChatCommandProcessor.processReceivedWX,
        "group-member-increase": WeChatCommandProcessor.processGroupMemberIncreasedWx,
        "message-withdraw": WeChatCommandProcessor.processMessageWithdrawnWX,
        "group-member-syn": WeChatCommandProcessor.processGroupMemberUpdateWX,
        "group-member-decease": WeChatCommandProcessor.processGroupMemberDecreasedWX,
        "offline":WeChatCommandProcessor.processOfflineWX,
        "online":WeChatCommandProcessor.processOnlineWx,
        "private-message-received":WeChatCommandProcessor.processPrivateMessageWX

    }
    r = RemoteCommandMapping.get(json.get("command"))
    if r:
        logging.info(f"接收到 {robot_id} 的命令 {json}")
        asyncio.create_task(r(json.get("message"), robot_id))