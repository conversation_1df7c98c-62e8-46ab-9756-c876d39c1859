from typing import Literal

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app.assets.public_assets import general_return_json
from app.core.database import get_db

from app.dict import command
from app.external_service import wechat_websocket_channel, qq_websocket_channel
from app.models.models import GroupRobot, Robot, Group

router = APIRouter(prefix="/administrator/force", tags=["api"])

@router.get("/synGroup")
async def synGroup(group_id:str, db: Session=Depends(get_db)):
    try:
        group = db.get(Group, group_id)

        if not group:
            return general_return_json(0x404,"Group Not exist")

        group_robot = db.query(GroupRobot).filter(GroupRobot.group_id == group.id).all()

        payload = command.synGroupMemberPayload(str(group.number))
        result = []
        for each_robot in group_robot:
            robot_this_round = db.query(Robot).filter(Robot.id == each_robot.robot_id,Robot.status == True).first()
            if not robot_this_round:
                continue
            else:
                result.append(each_robot)
            if group.type == '微信':
                await wechat_websocket_channel.send_message_to_robot(message=payload, robot=robot_this_round.account)
            else:
                await qq_websocket_channel.send_message_to_robot(message=payload, robot=robot_this_round.account)

        return general_return_json(0,f"SYN COMMAND SUBMITTED",result)
    except Exception as e:
        return general_return_json(-1,str(e))