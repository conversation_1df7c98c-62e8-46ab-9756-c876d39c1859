#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2019 Tianxiadatong, Co.,Ltd. All Rights Reserved.

This module defines a database core connection and session.
It contains MySQL and PostageSQL code both.
Using one after Annotating another first.

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06

// 因为数据库使用 KingBase 所以弃用 pymysql 使用 psycopg2
"""

# from sqlalchemy import create_engine
# from sqlalchemy.ext.declarative import declarative_base
# from sqlalchemy.orm import sessionmaker, Session
#
# DATABASE_URL = "mysql+pymysql://root:root@localhost:3306/robot?"
#
# engine = create_engine(
#     DATABASE_URL,
#     echo=True,
#     pool_pre_ping=True,
# )
#
# SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
#
# Base = declarative_base()
#
# def get_db():
#     """获取数据库会话。
#
#     这是一个生成器函数，用于创建和管理数据库会话。
#     它确保在使用完毕后正确关闭数据库连接。
#
#     Yields:
#         Session: SQLAlchemy 会话对象
#
#     Example:
#         db = next(get_db())
#         try:
#             # 使用数据库会话
#             ...
#         finally:
#             db.close()
#     """
#     db: Session = SessionLocal()
#     try:
#         yield db
#     finally:
#         db.close()
