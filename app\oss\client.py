#!/usr/bin/env python
# -*- coding:utf-8 -*-
import asyncio
import base64
import traceback
import logging
import threading
from obs import ObsClient
from config import OBSConfig
from typing import Tuple, Optional

# 初始化日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("OBSClient")

# 使用线程局部存储解决线程安全问题
_thread_local = threading.local()


def get_obs_client():
    """获取线程安全的OBS客户端实例"""
    if not hasattr(_thread_local, "obs_client"):
        _thread_local.obs_client = ObsClient(
            access_key_id=OBSConfig.ACCESS_KEY,
            secret_access_key=OBSConfig.SECRET_KEY,
            server=OBSConfig.ENDPOINT
        )
    return _thread_local.obs_client


def download_object_as_base64(object_name: str) -> <PERSON><PERSON>[bool, Optional[str]]:
    """从OBS下载对象并返回base64编码内容

    Args:
        object_name (str): OBS中的对象名称

    Returns:
        tuple: (操作状态, base64内容或错误信息)
    """
    try:
        # 获取线程安全的OBS客户端
        obs_client = get_obs_client()

        # 获取对象
        resp = obs_client.getObject(
            OBSConfig.BUCKET,
            object_name,
            loadStreamInMemory=True  # 确保内容加载到内存
        )

        if resp.status < 300:
            # 获取二进制内容
            binary_data = resp.body.buffer

            # 检查数据是否为空
            if binary_data is None or len(binary_data) == 0:
                error_msg = f"下载对象为空: {object_name}"
                logger.error(error_msg)
                return False, error_msg

            # 转换为base64
            base64_data = base64.b64encode(binary_data).decode('utf-8')

            logger.info(f"成功获取base64内容: {object_name} [大小: {len(binary_data)}字节]")
            return True, base64_data
        else:
            error_msg = f"OBS请求失败 [{resp.status}]: {object_name}"
            logger.error(error_msg)
            return False, error_msg

    except Exception as e:
        error_msg = f"获取base64内容异常: {str(e)}"
        logger.error(error_msg)
        logger.debug(traceback.format_exc())
        return False, error_msg


def upload_base64(base64_data: str, object_name: str, content_type: str = "application/octet-stream") -> Tuple[
    bool, str]:
    """上传base64编码内容到OBS

    Args:
        base64_data (str): base64编码的内容
        object_name (str): OBS中的对象名称
        content_type (str): 内容类型

    Returns:
        tuple: (操作状态, 错误信息或成功消息)
    """
    try:
        # 检查base64数据是否有效
        if not base64_data or len(base64_data) < 4:
            error_msg = "无效的base64数据: 数据为空或太短"
            logger.error(error_msg)
            return False, error_msg

        # 获取线程安全的OBS客户端
        obs_client = get_obs_client()

        # 解码base64数据
        try:
            binary_data = base64.b64decode(base64_data)
        except Exception as decode_error:
            error_msg = f"base64解码失败: {str(decode_error)}"
            logger.error(error_msg)
            return False, error_msg

        # 上传二进制数据
        resp = obs_client.putObject(
            OBSConfig.BUCKET,
            object_name,
            content=binary_data
        )

        if resp.status < 300:
            msg = f"上传base64成功: {object_name} [大小: {len(binary_data)}字节]"
            logger.info(msg)
            return True, msg
        else:
            error_msg = f"上传base64失败[{resp.status}]: {object_name}"
            logger.error(error_msg)
            return False, error_msg

    except Exception as e:
        error_msg = f"上传base64异常: {str(e)}"
        logger.error(error_msg)
        logger.debug(traceback.format_exc())
        return False, error_msg


# 异步获取base64
async def get_base64_from_obs(object_name: str) -> Tuple[bool, Optional[str]]:
    """异步获取OBS对象的base64编码内容"""
    try:
        status, result = await asyncio.to_thread(download_object_as_base64, object_name)
        return status, result
    except Exception as e:
        error_msg = f"异步操作异常: {str(e)}"
        logger.error(error_msg)
        return False, error_msg


# 异步上传base64
async def upload_base64_to_obs(base64_data: str, object_name: str, content_type: str = "application/octet-stream") -> \
Tuple[bool, str]:
    """异步上传base64编码内容到OBS"""
    try:
        status, result = await asyncio.to_thread(upload_base64, base64_data, object_name, content_type)
        return status, result
    except Exception as e:
        error_msg = f"异步上传异常: {str(e)}"
        logger.error(error_msg)
        return False, error_msg


async def get_image_data(object_name: str) -> Tuple[bool, Optional[str], Optional[str]]:
    """获取图片的base64数据和data URL

    Args:
        object_name (str): OBS中的对象名称

    Returns:
        tuple: (操作状态, base64数据, data URL)
    """
    try:
        # 获取base64数据
        status, base64_data = await get_base64_from_obs(object_name)
        if not status:
            return False, None, None
        
        # 根据文件扩展名确定MIME类型
        if object_name.lower().endswith(('.jpg', '.jpeg')):
            mime_type = "image/jpeg"
        elif object_name.lower().endswith('.png'):
            mime_type = "image/png"
        elif object_name.lower().endswith('.gif'):
            mime_type = "image/gif"
        else:
            mime_type = "image/jpeg"  # 默认
        
        # 生成data URL
        data_url = f"data:{mime_type};base64,{base64_data}"
        
        return True, base64_data, data_url
    except Exception as e:
        error_msg = f"获取图片数据失败: {str(e)}"
        logger.error(error_msg)
        return False, None, None

# if __name__ == "__main__":
    # 测试异步功能
    # async def main():
    #     # 测试上传base64
    #     # test_data = base64.b64encode(b"Hello, OBS!").decode('utf-8')
    #     # upload_success, msg = await upload_base64_to_obs(test_data, "test_file.txt", "text/plain")
    #     # 测试图片上传
    #     test_image_url = "EhROKC9ZvFtl26Om3r1w9q86LV.jpg"
    #     with open(test_image_url, "rb") as f:
    #         test_data = base64.b64encode(f.read()).decode('utf-8')
    #
    #     print(test_data)
    #
    #     upload_success, msg = await upload_base64_to_obs(test_data, "EhROKC9ZvFtl26Om3r1w9q86LV.jpg", "image/png")
    #     if upload_success:
    #         print(f"上传成功: {msg}")
    #     else:
    #         print(f"上传失败: {msg}")
    #
    #     # 测试获取base64
    #     status, base64_data = await get_base64_from_obs("EhROKC9ZvFtl26Om3r1w9q86LV.jpg")
    #     if status:
    #         print(f"获取base64成功: 数据长度 {len(base64_data)}")
    #         print(base64_data)
    #         # 转化为二进制数据并保存到文件
    #         with open("downloaded_image.jpg", "wb") as f:
    #             f.write(base64.b64decode(base64_data))
    #     else:
    #         print(f"获取失败: {base64_data}")
    #
    #     # 测试获取不存在的文件
    #     status, result = await get_base64_from_obs("non_existent_file.txt")
    #     if not status:
    #         print(f"预期中的失败: {result}")
    #
    #
    # # 运行异步函数
    # asyncio.run(main())
    # print(generate_temp_url("Default.jpg", 3600))
