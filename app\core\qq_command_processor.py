#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2019 Tianxiadatong, Co., Ltd. All Rights Reserved.

This module defines event handlers for various QQ-robot-related actions,
including message reception, member changes,modifying online/offline status and auditing logic.

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06
"""

import json
import logging
import re
import uuid
import traceback
from datetime import datetime
from sqlalchemy import or_

from app.api import audit
from app.api.audit import Audit_ModifyMapper
from app.core.database import get_db
from app.dict import command
from app.dict.schema import MessagePacket
from app.core.deal import parse_execution_date, process_deal_data, process_discount_rates

from app.external_service import third_party, qq_websocket_channel

from app.models.models import Message, Task, Audit, Instruction, Robot, GroupRobot, Group, Script, GroupMember, User, \
    UserRole, BbTFundInfo, BbTTraderival, BbTHgRegister, BbTTraderivalLinkman, GroupProduction

from sqlalchemy.orm import Session


# 审核状态枚举 - 使用位标志设计，便于组合和判断
class AuditStatus:
    # 基础状态
    PENDING = 0  # 待处理
    INVALID = 1  # 已失效
    CONFIRMED = 2  # 已确认

    # 状态修饰符（可与基础状态组合）
    WITHDRAWN = 10  # 已撤回
    MISSING_FIELDS = 100  # 要素不全
    NO_PERMISSION = 1000  # 无权限
    MODIFIED = 10000  # 已更改


def get_fund_info(db, production_name, group=None):
    """查询产品信息，如果唯一匹配则返回 fund_id 和 fund_caption，否则返回 None"""
    if not production_name:
        return None

    replace_list = ["外贸信托", "-"]
    for item in replace_list:
        production_name = production_name.replace(item, "")
    if group:
        fund_info = db.query(BbTFundInfo).join(GroupProduction, BbTFundInfo.fund_code == GroupProduction.production_id).filter(
            GroupProduction.group_id == group.id,
            BbTFundInfo.fund_caption.ilike(f"%{production_name}%")
        ).all()
    else:
        return None

    # logging.info(f"查询到的产品信息: {len(fund_info)} 条")
    if len(fund_info) == 1:
        # logging.info(f"唯一匹配的产品信息: {fund_info[0].fund_code}, {fund_info[0].fund_caption}")
        return {
            "production_id": fund_info[0].fund_code,
            "production": fund_info[0].fund_name,
            "production_fund_caption": fund_info[0].fund_name,
        }
    return None


def get_rival_info(db, rival_name, rival_trader_name=None, rival_trader_code=None):
    rival_data = {
        "rival": "",
        "rival_id": "",
        "rival_interbank_organ_code": "",
        "rival_trader": "",
        "rival_trader_code": "",
        "rival_code": "",
        "rival_trader_id": "",
    }

    """查询对手方信息"""
    logging.info(f"查询对手方信息: rival_name={rival_name}, rival_trader_name={rival_trader_name}, rival_trader_code={rival_trader_code}")
    if rival_name and rival_name != "缺失":
        # 根据 rival_name 做全称以及简称的匹配
        flag = False
        rival_info = db.query(BbTTraderival).filter(
            or_(
                BbTTraderival.traderival_name == rival_name,
                BbTTraderival.rival_fullname == rival_name
            )
        ).all()
        if rival_info:
            flag = True
            # logging.info(f"查询到对手方信息: {rival_info[0].traderival_name}")
            rival_data.update({
                "rival": rival_info[0].traderival_name,
                "rival_id": rival_info[0].rival_id,
                "rival_interbank_organ_code": rival_info[0].interbank_organ_code,
                "rival_code": rival_info[0].rival_code,
            })


        if not flag:
            # 模糊查询
            rival_info = db.query(BbTTraderival).filter(
                or_(
                    BbTTraderival.traderival_name.like(f"%{rival_name}%"),
                    BbTTraderival.rival_fullname.like(f"%{rival_name}%")
                )
            ).all()

            if len(rival_info) == 1:
                # logging.info(f"模糊查询到对手方信息: {rival_info[0].traderival_name}")
                rival_data.update({
                    "rival":  rival_info[0].traderival_name,
                    "rival_id": rival_info[0].rival_id,
                    "rival_interbank_organ_code": rival_info[0].interbank_organ_code,
                    "rival_code": rival_info[0].rival_code,
                })

        # 继续查询 rival_trader_name
        if rival_trader_name and not rival_data["rival_code"] and rival_data.get("rival_id"):
            linkman = db.query(BbTTraderivalLinkman).filter(
                BbTTraderivalLinkman.linkman.like(f"%{rival_trader_name}%"),
                BbTTraderivalLinkman.rival_id == rival_data.get("rival_id")
            ).first()

            if linkman:
                # logging.info(f"查询到对手方交易员信息: {linkman.linkman}")
                rival_data.update({
                    "rival_trader": linkman.linkman,
                    "rival_trader_code": linkman.rival_tradercode,
                    "rival_code": rival_data["rival_code"],
                    "rival_trader_id": linkman.contact_id,
                })


    if rival_trader_code:
        logging.info(f"查询对手方交易员信息: rival_trader_code={rival_trader_code}")
        # 如果提供了对手方交易员 ID，查询对应的对手方信息
        linkman = db.query(BbTTraderivalLinkman).filter(
            BbTTraderivalLinkman.rival_tradercode == rival_trader_code
        ).first()

        if linkman:
            rival_code = db.query(BbTTraderival).filter(
                BbTTraderival.rival_id == linkman.rival_id
            ).first()
            if rival_code:
                # logging.info(f"查询到对手方交易员信息: {linkman.linkman}")
                rival_data.update({
                    "rival_trader": linkman.linkman,
                    "rival_trader_code": linkman.rival_tradercode,
                    "rival_code": rival_code.rival_code,
                    "rival_trader_id": linkman.contact_id,
                    "rival": rival_code.traderival_name,
                    "rival_interbank_organ_code": rival_code.interbank_organ_code,
                })
        else:
            rival_data.update({
                "rival_trader": "",
                "rival_trader_code": "",
                "rival_trader_id": ""
            })

    # 查询联系人（如果提供）
    if rival_trader_name and not rival_data["rival_trader_code"]:
        logging.info(f"查询联系人: rival_trader_name={rival_trader_name}")
        link_man = db.query(BbTTraderivalLinkman).filter(
            BbTTraderivalLinkman.linkman.like(f"%{rival_trader_name}%"),
        ).all()

        # 如果对手方 rival_id 不为空，限定查询
        if rival_data.get("rival_id") and rival_data.get("rival_id") != "":
            logging.info(f"对手方ID: {rival_data.get('rival_id')}, 查询联系人: {rival_trader_name}")
            link_man = db.query(BbTTraderivalLinkman).filter(
                BbTTraderivalLinkman.linkman.like(f"%{rival_trader_name}%"),
                BbTTraderivalLinkman.rival_id == rival_data.get("rival_id"),
            ).all()

        if len(link_man) == 1:
            rival_code = db.query(BbTTraderival).filter(
                BbTTraderival.rival_id.like(f"%{link_man[0].rival_id}%")
            ).all()
            rival_data.update({
                "rival_trader": link_man[0].linkman,
                "rival_trader_code": link_man[0].rival_tradercode,
                "rival_code": rival_code[0].rival_code,
                "rival_trader_id": link_man[0].contact_id,
                "rival": rival_code[0].traderival_name,
                "rival_interbank_organ_code": rival_code[0].interbank_organ_code,
            })
        else:
            rival_data.update({
                "rival_trader": "",
                "rival_trader_code": "",
                "rival_trader_id": ""
            })

    logging.info(f"最终对手方数据: {rival_data}")


    return rival_data


def model_to_dict(instance, only_none=False):
    data = {}
    for column in instance.__table__.columns:
        value = getattr(instance, column.name)
        if only_none:
            if value is None:
                data[column.name] = value
        else:
            data[column.name] = value
    return data


async def send_reply_message(robot, MessagePack, content, reply_to, robot_channel, db):
    reply_packet = MessagePacket(
        sender=robot.nickname,
        receiver=str(MessagePack["sender"]),
        content=content,
        position=str(MessagePack["position"]),
        type=1,
        channel=robot_channel,
        reply=1,
        sequence=MessagePack["sequence"]
    )
    await qq_websocket_channel.send_message_to_robot(message=command.AtSendWIthReplyPayload(reply_packet),
                                                     robot=robot_channel)
    db.add(Message(
        sender=robot.nickname,
        receiver=str(MessagePack["sender"]),
        content=content,
        position=MessagePack["position"],
        type=1,
        reply=reply_to,
        sequence=-1,
        channel=robot_channel,
        role='robot'
    ))
    db.flush()


async def send_received_reply(robot, MessagePack, robot_channel, reply_to, db):
    await send_reply_message(robot, MessagePack, "收到", reply_to, robot_channel, db)


async def processReceivedQQ(MessagePack: dict, robot_channel: str):
    """处理接收到的QQ消息。

       处理新接收到的QQ消息，包括消息存储、任务创建、指令识别和审核项创建等流程。
       会检查机器人和群组的启用状态，确保消息处理的有效性。

       Args:
           MessagePack (dict): 消息包数据，包含以下字段：
               - sender: 发送者
               - receiver: 接收者
               - content: 消息内容
               - position: 群号
               - type: 消息类型
               - reply: 回复的消息ID
               - sequence: 消息序号
               - channel: 机器人通道
           robot_channel (str): 机器人通道标识

       Raises:
           Exception: 数据库操作异常或消息处理失败
       """
    db_gen = get_db()
    db: Session = next(db_gen)
    try:
        # 机器人已禁用？
        robot = db.query(Robot).filter(
            Robot.account == str(MessagePack["channel"]),
            Robot.enable == True
        ).first()
        if not robot:
            logging.info("信息被拦截:ROBOT未启动")
            return

        # 群已禁用（总控）？
        group = db.query(Group).filter(
            Group.number == MessagePack["position"],
            Group.listening == True, Group.type == 'QQ'
        ).first()

        # logging.info(f"处理群组 {MessagePack['position']} 的消息，群组状态: {group}")

        if not group:
            logging.info("信息被拦截:GROUP未启动")
            #
            return
        # 群已禁用（分控）？
        group_robots = db.query(GroupRobot).filter(
            GroupRobot.group_id == group.id, GroupRobot.robot_id == robot.id
        ).all()

        if not group_robots or robot.id not in [gr.robot_id for gr in group_robots]:
            print(f"{group.id}{robot.id}")
            logging.info("信息被拦截:GROUP_ROBOT未启动")
            return

        # is_another_robot = db.query(Robot).filter(Robot.nickname == str(MessagePack["sender"]),
        #                                           Robot.type == 'QQ').first()
        # if is_another_robot:
        #     logging.info("信息被拦截:信息由另一个ROBOT发送")
        #     return

        # group_member = db.query(GroupMember).filter(GroupMember.name == MessagePack["sender"]).first()
        # name = MessagePack["sender"] and group_member.group_id == group.id
        group_member = db.query(GroupMember).filter(
            GroupMember.name == MessagePack["sender"],
            GroupMember.group_id == group.id
        ).first()

        logging.info(f"处理群成员 {MessagePack['sender']} 的消息，成员状态: {group_member.role}, MsgPack: {MessagePack}")
        if group_member.role == 'trader' and (not (
                str('@' + robot.account) in MessagePack['content'] or str('@' + robot.nickname) in MessagePack[
            'content'] or ("CQ:at,qq=" + robot.account) in MessagePack['content'])):
            logging.info(f"信息被拦截:外贸员工未使用@ : {robot.nickname}机器人")
            return

        convert = Message(
            sender=MessagePack["sender"],
            receiver=MessagePack["receiver"],
            content=MessagePack["content"],
            position=MessagePack["position"],
            type=MessagePack["type"],
            reply=MessagePack["reply"],
            sequence=MessagePack["sequence"],
            channel=MessagePack["channel"],
        )

        if MessagePack['reply'] is not None:
            db_msg = db.query(Message).filter(Message.sequence == MessagePack['reply']).first()

            if db_msg:
                convert.reply = db_msg.id

        db.add(convert)

        db.flush()
        db.commit()

        logging.info(f"机器人 {robot.nickname} 接收到来自QQ群 {convert.position} 的信息并已存储, Message-ID {convert.id}")

        intention_result = await third_party.launch_recognition_task(convert.id)

        rewrite_message = db.get(Message, convert.id)
        rewrite_message.result = json.dumps(intention_result, indent=1, )

        logging.info(f"{convert.id} 结果已写入Message.result")

        db.commit()

        # print(f"intention_recg_result = {intention_result}")

        if intention_result.get('code') != 0:
            logging.error(f"意图识别错误:{intention_result}")
            return

        intention_type = intention_result.get('data', {}).get('intentions', [{}])[0].get('intention_type', 3)
        intention_type = int(intention_type)

        logging.info(f"意 图 识 别 返 回类 型 :{intention_type}")

        if intention_type == 4:
            return

        # 要素更改分支
        if intention_type == 2:

            # flashback_task = db.query(Instruction).filter(
            #    Instruction.task_id == intention_result.get('data', {}).get('task_id', '')).first()
            logging.info(f"意图识别结果=---------------------------------------: {intention_result}, 要素更改")

            task = Task(message_id=convert.id, id=intention_result.get('data', {}).get('task_id'))

            while db.query(Task).filter(Task.id == task.id).first():
                task.id = str(uuid.uuid4())[:16]

            db.add(task)
            
            # 查看 orders 是否为空，如果为空，直接 返回
            if not intention_result.get('data', {}).get('orders', []):
                logging.info("意图识别结果中没有订单数据，直接返回")
                return

            complement = True
            for order in intention_result.get('data', {}).get('orders', []):
                db.add(Instruction(
                    id=order.get('order_id'),
                    task_id=intention_result.get('data', {}).get('task_id', None)
                ))
                db.flush()

                structure = order.get("order_structure", {})
                data = {
                    "variety": order.get("breed_type"),
                    "market": order.get("market_type"),
                    "classification": order.get("order_type"),
                    "execution": parse_execution_date(structure.get("执行日")) if structure.get(
                        "执行日") else datetime.today(),
                    "production": structure.get("产品名称"),
                    "production_fund_caption": "",
                    "direction": structure.get("方向"),
                    "three_party_sequel": structure.get("三方续作"),
                    "security_code": re.sub(r"\D", "", structure.get("证券代码", "")),
                    "rival_interbank_organ_code": "",
                    "security_name": structure.get("证券名称"),
                    "yield_rate": structure.get("收益率"),
                    "net_price": structure.get("净价/委托价格"),
                    "full_price": structure.get("全价"),
                    "yield_to_maturity": structure.get("到期收益率"),
                    "option_yield": structure.get("行权收益率"),
                    "quantity": audit.parse_number_with_unit(structure.get("数量")),
                    "denomination": structure.get("金额/面额"),
                    "liquidation_speed": (
                        "T+0" if "+0" in structure.get("清算速度", "").upper() or "T+0" in structure.get("清算速度",
                                                                                                         "").upper()
                        else "T+1" if "+1" in structure.get("清算速度", "").upper() or "T+1" in structure.get(
                            "清算速度", "").upper()
                        else None),
                    "rival": structure.get("对手⽅"),
                    "rival_trader": structure.get('对手方交易员'), "rival_seat": structure.get('对手席位'),
                    "rival_trader_id": structure.get("对手交易员号"),
                    "agreement_number": structure.get("约定号"),
                    "payment_method": structure.get("结算方式"),
                    "declaration_type": structure.get("申报类型"),
                    "rival_dealer_code": structure.get("对手交易商代码"),
                    "trader_entity_code": structure.get("交易商主体代码"),
                    "rival_trader_code": structure.get("对手交易员代码"),
                    "deadline": structure.get("期限"),
                    "violation_grace_period": structure.get("违约宽限期"),
                    "supplementary_term": structure.get("补充条款"),
                    "share_bonus_method": structure.get("分红方式"),
                    "distribution_channel": None,
                    "pledge_coupon_code": structure.get("质押券代码"),
                    "pledge_coupon_name": structure.get("质押券名称"),
                    "pledge_quantity": structure.get("质押数量"),
                    "discount_rate": structure.get("折扣率"),
                    "execution_str": structure.get("执行⽇"),
                    "message": convert.id,
                    "instruction_id": order.get("order_id"),
                    "production_id": structure.get("产品代号"),
                }

                # 检查是否有必要的要素
                logging.info(f"要素检查: {data}")

                # 从数据表 匹配相关数据
                if data["production"]:
                    fund_data = get_fund_info(db, data["production"], group)
                    if fund_data:
                        data.update(fund_data)
                    else:
                        # 如果查询失败，清空相关字段
                        data.update({
                            "production_id": "",
                            "production_fund_caption": "",
                        })


                # if data["rival"]:
                rival_data = get_rival_info(db, data["rival"], data.get("rival_trader"), data.get("rival_trader_code"))
                if rival_data:
                    logging.info(f"查询到对手方信息TRUEEEEEEE: {rival_data}")
                    data.update(rival_data)
                else:
                    # 如果查询失败，清空相关字段
                    logging.info(f"查询到对手方信息FALSEEEEEEE: {data['rival']}")
                    data.update({
                        "rival": "",
                        "rival_id": "",
                        "rival_interbank_organ_code": "",
                        "rival_trader": "",
                        "rival_trader_code": "",
                        "rival_trader_id": ""
                    })

                logging.info(f"要素新增，待增加AUDIT: {data}")

                data = process_deal_data(data)

                # 创建 Audit 实例
                NewAudit = Audit(
                    variety=data["variety"],
                    market=data["market"],
                    classification=data["classification"],
                    execution=data["execution"],
                    production=data["production"],
                    production_fund_caption=data.get("production_fund_caption", ""),
                    direction=(
                        audit.Audit_ModifyMapper
                        .get(audit.reverse_category_mapping.get(data.get('variety'), {}).get(data.get('market')),
                             {})
                        .get(data.get('classification'), {})
                        .get(data.get('direction'))
                    ),

                    three_party_sequel=data.get("three_party_sequel"),
                    security_code=str(data.get("security_code")).split(".")[0],
                    security_name=str(data.get("security_name")).split(".")[0],
                    yield_rate=data.get("yield_rate").replace("%","") if  data.get("yield_rate") else "",
                    net_price=data.get("net_price"),
                    full_price=data.get("full_price"),
                    yield_to_maturity=data.get("yield_to_maturity"),
                    option_yield=data.get("option_yield"),
                    quantity=data.get("quantity"),
                    denomination=data.get("denomination"),
                    liquidation_speed=data.get("liquidation_speed"),
                    rival=data.get("rival"),
                    rival_interbank_organ_code=data.get("rival_interbank_organ_code"),
                    rival_trader=data.get("rival_trader"),
                    rival_seat=data.get("rival_seat"),
                    rival_code=data.get("rival_code"),
                    rival_trader_id=data.get("rival_trader_id"),
                    agreement_number=data.get("agreement_number"),
                    payment_method=data.get("payment_method"),
                    declaration_type=data.get("declaration_type"),
                    rival_dealer_code=data.get("rival_dealer_code"),
                    trader_entity_code=data.get("trader_entity_code"),
                    rival_trader_code=data.get("rival_trader_code"),
                    deadline=data.get("deadline"),
                    violation_grace_period=data.get("violation_grace_period"),
                    supplementary_term=data.get("supplementary_term"),
                    share_bonus_method=data.get("share_bonus_method"),
                    distribution_channel=data.get("distribution_channel"),
                    # pledge_coupon_code=str(data.get("pledge_coupon_code")),
                    pledge_coupon_code="、".join(
                        i.split(".")[0] for i in data.get("pledge_coupon_code").split("、")) if data.get(
                        "pledge_coupon_code") else data.get("pledge_coupon_code"),
                    pledge_coupon_name=data.get("pledge_coupon_name"),
                    pledge_quantity=data.get("pledge_quantity"),
                    discount_rate=process_discount_rates(data.get("discount_rate")),
                    message=data["message"],
                    instruction_id=data["instruction_id"],
                    status=10100 if order.get('is_order_complete') is False else 10000,
                    production_id=data.get('production_id'),
                )

                logging.info(f"创建了新的AUDIT: {NewAudit.direction}")
                db.add(NewAudit)
                db.flush()

                complement_struct = []
                if order.get('is_order_complete') is False:
                    complement = False

                    build_appendant = {
                        '品种': data["variety"],
                        '业务分类': data["classification"],
                        '市场': data["market"],
                        **structure,
                    }

                    complement_struct.append(build_appendant)
                    complement_struct.append({"—————————————————"})
                else:
                    complement = True

            logging.info(f"adfjadlfajdfjaldfjadjfald， {complement}")
            print(
                f"本次意图识别权限 {group_member.permission & 0b100} {group_member.permission & 0b010} {group_member.permission & 0b001},意图识别类型{(intention_type)}")
            intention_type = int(intention_type)

            # 判断三个权限
            if (not group_member or (group_member.permission & 0b100 == 0 and intention_type == 1) or
                    (group_member.permission & 0b010 == 0 and intention_type == 4) or (
                            group_member.permission & 0b001 == 0 and intention_type == 5)
            ):
                logging.info("信息被拦截:PERMISSION不足")

                if intention_type != 1:
                    db.commit()
                    return

                buildContent = db.query(Script).filter(Script.environment == '群聊无权限话术').first().content

                packed = MessagePacket(
                    sender=robot.nickname,
                    receiver=str(MessagePack["sender"]),
                    content=buildContent,
                    position=str(MessagePack["position"]),
                    type=1,
                    channel=robot_channel,
                    reply=1,
                    sequence=MessagePack['sequence']
                )

                db.flush()
                construct = Message(
                    sender=MessagePack["receiver"],
                    receiver=MessagePack["sender"],
                    content=packed.content,
                    position=MessagePack["position"],
                    type=1,
                    reply=convert.id,
                    sequence=-1,
                    channel=MessagePack["channel"],
                )
                db.add(construct)
                await qq_websocket_channel.send_message_to_robot(message=command.AtSendWIthReplyPayload(packed),
                                                                 robot=robot_channel)
                db.commit()
                return

            if complement is False:
                logging.info("信息被拦截:要素不足")

                pretty_json = re.sub(
                    r'[\[\]\{\}"\',]', '',
                    json.dumps(complement_struct, indent=1, ensure_ascii=False, default=str)
                )

                buildContent = db.query(Script).filter(
                    Script.environment == '要素补全话术').first().content + pretty_json

                convert = Message(
                    sender=MessagePack["sender"],
                    receiver=MessagePack["receiver"],
                    content=MessagePack["content"],
                    position=MessagePack["position"],
                    type=MessagePack["type"],
                    reply=MessagePack["reply"],
                    sequence=MessagePack["sequence"],
                    channel=MessagePack["channel"],
                )
                packed = MessagePacket(
                    sender=robot.nickname,
                    receiver=str(MessagePack["sender"]),
                    content=buildContent,
                    position=str(MessagePack["position"]),
                    type=1,
                    channel=robot_channel,
                    reply=1,
                    sequence=MessagePack['sequence']
                )
                db.add(convert)
                db.flush()

                await qq_websocket_channel.send_message_to_robot(message=command.AtSendWIthReplyPayload(packed),
                                                                 robot=robot_channel)
                db.commit()
                return

            constructMessagePack = MessagePacket(
                sender=robot.nickname,
                receiver=str(MessagePack["sender"]),
                content="收到",
                position=str(MessagePack["position"]),
                type=1,
                channel=robot_channel,
                reply=1,
                sequence=MessagePack['sequence'],

            )

            await qq_websocket_channel.send_message_to_robot(robot=robot_channel,
                                                             message=command.AtSendWIthReplyPayload(
                                                                 constructMessagePack))
            constructMessage = Message(
                sender=constructMessagePack.sender,
                receiver=constructMessagePack.receiver,
                content=constructMessagePack.content,
                position=constructMessagePack.position,
                type=constructMessagePack.type,
                reply=convert.id,
                sequence=-1,
                role='robot'
            )

            db.add(constructMessage)

            db.commit()
            return

        # if intention_type = 1 , blowing code will be executed

        # 创建任务
        task = Task(message_id=convert.id, id=intention_result.get('data', {}).get('task_id'))
        while db.query(Task).filter(Task.id == task.id).first():
            task.id = str(uuid.uuid4())[:16]
        db.add(task)
        db.flush()

        for each in intention_result.get('data', {}).get('orders', []):
            instruct = Instruction(task_id=intention_result.get('data', {}).get('task_id'),
                                   id=each.get('order_id', 'LOST'))
            db.add(instruct)
            db.flush()

        db.commit()

        logging.info(
            f"机器人 {robot.nickname} 接收到来自QQ群 {convert.position} 的信息并已处理, Message-ID {convert.id}")
        intention_type = int(intention_type)
        if (not group_member or (group_member.permission & 0b100 == 0 and intention_type == 1) or
                (group_member.permission & 0b010 == 0 and intention_type == 4) or (
                        group_member.permission & 0b001 == 0 and intention_type == 5)
        ):
            access = False
        else:
            access = True

        orders = intention_result["data"]["orders"]
        # 查看 orders 是否为空，如果为空，直接 返回
        if not intention_result.get('data', {}).get('orders', []):
            logging.info("意图识别结果中没有订单数据，直接返回")
            return

        complement = True
        for order in orders:
            structure = order["order_structure"]
            logging.info(f"结构体: {structure}")
            data = {"variety": order.get("breed_type"), "market": order.get("market_type"),
                    "classification": order.get("order_type"),
                    "execution": parse_execution_date(structure.get("执行日")) if structure.get(
                        "执行日") else datetime.today(),
                    "production": structure.get("产品名称"),
                    "production_fund_caption": "",
                    "direction": structure.get("方向"),
                    "three_party_sequel": structure.get("三方续作"),
                    "security_code": re.sub(r"\D", "", structure.get("证券代码", "")),
                    "security_name": structure.get("证券名称"),
                    "yield_rate": structure.get("收益率"), "net_price": structure.get("净价/委托价格"),
                    "full_price": None,
                    "yield_to_maturity": structure.get("到期收益率"), "option_yield": structure.get("行权收益率"),
                    "quantity": audit.parse_number_with_unit(structure.get("数量")),
                    "denomination": structure.get('金额/面额'),
                    "rival_interbank_organ_code": "",
                    "liquidation_speed": (
                        "T+0" if "+0" in structure.get("清算速度", "").upper() or "T+0" in structure.get("清算速度",
                                                                                                         "").upper()
                        else "T+1" if "+1" in structure.get("清算速度", "").upper() or "T+1" in structure.get(
                            "清算速度", "").upper()
                        else None
                    ), "rival": structure.get("对手方"),
                    "rival_trader": structure.get('对手方交易员'), "rival_seat": structure.get('对手席位'),
                    "rival_trader_id": structure.get("对手交易员号"),
                    "agreement_number": structure.get("约定号"), "payment_method": structure.get("结算方式"),
                    "declaration_type": structure.get("申报类型"),
                    "rival_dealer_code": structure.get("对手交易商代码"),
                    "trader_entity_code": structure.get("交易商主体代码"),
                    "rival_trader_code": structure.get("对手交易员代码"), "deadline": structure.get('期限'),
                    "violation_grace_period": structure.get("违约宽限期"),
                    "supplementary_term": structure.get("补充条款"), "share_bonus_method": None,
                    "distribution_channel": None, "pledge_coupon_code": structure.get("质押券代码"),
                    "pledge_coupon_name": structure.get("质押券名称"),
                    "pledge_quantity": structure.get('质押数量'), "discount_rate": structure.get("折扣率"),
                    "execution_str": structure.get("执行⽇"), "status": 0,
                    "message": convert.id, "instruction_id": order.get("order_id"),
                    "production_id": structure.get("产品代号"),
                    }

            # 从数据表 匹配相关数据
            if data["production"]:
                fund_data = get_fund_info(db, data["production"], group)
                if fund_data:
                    data.update(fund_data)
                else:
                    # 如果查询失败，清空相关字段
                    data.update({
                        "production_id": "",
                        "production_fund_caption": "",
                    })

            # if data["rival"]:
            rival_data = get_rival_info(db, data["rival"], data.get("rival_trader"), data.get("rival_trader_code"))
            if rival_data:
                data.update(rival_data)
                logging.info(f"对手方数据------adfadfad: {data}")
            else:
                # 如果查询失败，清空相关字段
                data.update({
                    "rival": "",
                    "rival_id": "",
                    "rival_interbank_organ_code": "",
                    "rival_trader": "",
                    "rival_trader_code": "",
                    "rival_trader_id": ""
                })

            data = process_deal_data(data)

            # 创建 Audit 实例
            NewAudit = Audit(
                variety=data["variety"],
                market=data["market"],
                classification=data["classification"],
                execution=data["execution"],
                production=data["production"],
                production_fund_caption=data.get("production_fund_caption", ""),
                direction=(
                    audit.Audit_ModifyMapper
                    .get(audit.reverse_category_mapping.get(data.get('variety'), {}).get(data.get('market')), {})
                    .get(data.get('classification'), {})
                    .get(data.get('direction'))
                ),

                three_party_sequel=data.get("three_party_sequel"),
                security_code=str(data.get("security_code")).split(".")[0],
                security_name=str(data.get("security_name")).split(".")[0],
                yield_rate=data.get("yield_rate").replace("%","") if  data.get("yield_rate") else "",
                net_price=data.get("net_price"),
                full_price=data.get("full_price"),
                yield_to_maturity=data.get("yield_to_maturity"),
                option_yield=data.get("option_yield"),
                quantity=data.get("quantity"),
                denomination=data.get("denomination"),
                liquidation_speed=data.get("liquidation_speed"),
                rival=data.get("rival"),
                rival_interbank_organ_code=data.get("rival_interbank_organ_code"),
                rival_trader=data.get("rival_trader"),
                rival_code=data.get("rival_code"),
                rival_seat=data.get("rival_seat"),
                rival_trader_id=data.get("rival_trader_id"),
                agreement_number=data.get("agreement_number"),
                payment_method=data.get("payment_method"),
                declaration_type=data.get("declaration_type"),
                rival_dealer_code=data.get("rival_dealer_code"),
                trader_entity_code=data.get("trader_entity_code"),
                rival_trader_code=data.get("rival_trader_code"),
                deadline=data.get("deadline"),
                violation_grace_period=data.get("violation_grace_period"),
                supplementary_term=data.get("supplementary_term"),
                share_bonus_method=data.get("share_bonus_method"),
                distribution_channel=data.get("distribution_channel"),
                # pledge_coupon_code=str(data.get("pledge_coupon_code")),
                pledge_coupon_code="、".join(i.split(".")[0] for i in data.get("pledge_coupon_code").split("、")) if data.get("pledge_coupon_code") else data.get("pledge_coupon_code"),
                pledge_coupon_name=data.get("pledge_coupon_name"),
                pledge_quantity=data.get("pledge_quantity"),
                discount_rate=process_discount_rates(data.get("discount_rate")),
                message=data["message"],
                instruction_id=data["instruction_id"],
                status=100 if order.get('is_order_complete') is False else 0,
                production_id=data.get('production_id'),
            )

            print(f"创建了新的AUDIT: {NewAudit.direction}")

            all_intention_types = intention_result.get('data', {}).get('intentions', [])
            logging.info(f"意图识别结果中的意图类型: {all_intention_types}")
            logging.info(f"意图识别结果中的意图类型数量: {len(all_intention_types)}")
            # 检查 intention_type len 是否 大于 1, 指令改为 已更改
            if len(all_intention_types) > 1:
                NewAudit.status = 10100 if order.get('is_order_complete') is False else 10000,
                logging.info(f"意图识别结果中有多个意图类型，设置状态为 {NewAudit.status}")

            if access is False:
                NewAudit.status += 1000

            complement_struct = []
            if order.get('is_order_complete') is False:
                complement = False

                build_appendant = {
                    '品种': data["variety"],
                    '业务分类': data["classification"],
                    '市场': data["market"],
                    **structure,
                }

                complement_struct.append(build_appendant)
                complement_struct.append({"—————————————————"})
            else:
                complement = True

            db.add(NewAudit)
            db.flush()

        print(
            f"本次意图识别权限 {group_member.permission & 0b100} {group_member.permission & 0b010} {group_member.permission & 0b001},意图识别类型{(intention_type)}")
        intention_type = int(intention_type)

        # 判断三个权限
        if (not group_member or (group_member.permission & 0b100 == 0 and intention_type == 1) or
                (group_member.permission & 0b010 == 0 and intention_type == 4) or (
                        group_member.permission & 0b001 == 0 and intention_type == 5)
        ):
            logging.info("信息被拦截:PERMISSION不足")

            if intention_type != 1:
                db.commit()
                return

            buildContent = db.query(Script).filter(Script.environment == '群聊无权限话术').first().content

            packed = MessagePacket(
                sender=robot.nickname,
                receiver=str(MessagePack["sender"]),
                content=buildContent,
                position=str(MessagePack["position"]),
                type=1,
                channel=robot_channel,
                reply=1,
                sequence=MessagePack['sequence']
            )

            db.flush()
            construct = Message(
                sender=MessagePack["receiver"],
                receiver=MessagePack["sender"],
                content=packed.content,
                position=MessagePack["position"],
                type=1,
                reply=convert.id,
                sequence=-1,
                channel=MessagePack["channel"],
            )
            db.add(construct)
            await qq_websocket_channel.send_message_to_robot(message=command.AtSendWIthReplyPayload(packed),
                                                             robot=robot_channel)
            db.commit()
            return

        if complement is False:
            logging.info("信息被拦截:要素不足")

            pretty_json = re.sub(
                r'[\[\]\{\}"\',]', '',
                json.dumps(complement_struct, indent=1, ensure_ascii=False, default=str)
            )

            buildContent = db.query(Script).filter(
                Script.environment == '要素补全话术').first().content + pretty_json

            convert = Message(
                sender=MessagePack["sender"],
                receiver=MessagePack["receiver"],
                content=MessagePack["content"],
                position=MessagePack["position"],
                type=MessagePack["type"],
                reply=MessagePack["reply"],
                sequence=MessagePack["sequence"],
                channel=MessagePack["channel"],
            )
            packed = MessagePacket(
                sender=robot.nickname,
                receiver=str(MessagePack["sender"]),
                content=buildContent,
                position=str(MessagePack["position"]),
                type=1,
                channel=robot_channel,
                reply=1,
                sequence=MessagePack['sequence']
            )
            db.add(convert)
            db.flush()

            await qq_websocket_channel.send_message_to_robot(message=command.AtSendWIthReplyPayload(packed),
                                                             robot=robot_channel)
            db.commit()
            return

        db.commit()

        constructMessagePack = MessagePacket(
            sender=robot.nickname,
            receiver=str(MessagePack["sender"]),
            content="收到",
            position=str(MessagePack["position"]),
            type=1,
            channel=robot_channel,
            reply=1,
            sequence=MessagePack['sequence'],

        )

        await qq_websocket_channel.send_message_to_robot(robot=robot_channel,
                                                         message=command.AtSendWIthReplyPayload(
                                                             constructMessagePack))
        constructMessage = Message(
            sender=constructMessagePack.sender,
            receiver=constructMessagePack.receiver,
            content=constructMessagePack.content,
            position=constructMessagePack.position,
            type=constructMessagePack.type,
            reply=convert.id,
            sequence=-1,
            role='robot'
        )

        db.add(constructMessage)

        db.commit()
    except Exception as e:
        db.rollback()
        raise e
    finally:
        db_gen.close()


async def processGroupMemberIncreasedQQ(NewMemberInfo: dict, robot_channel: str):
    """处理QQ群成员增加事件。

    当有新成员加入QQ群时，处理欢迎消息发送和成员信息记录。
    包括检查机器人和群组状态、获取欢迎语脚本、记录新成员信息等。

    Args:
        NewMemberInfo (dict): 新成员信息，包含以下字段：
            - user_id: 用户QQ号
            - nickname: 用户昵称
            - group_id: 群号
        robot_channel (str): 机器人通道标识

    Raises:
        Exception: 数据库操作异常
    """
    db_gen = get_db()
    db: Session = next(db_gen)
    try:
        robot = db.query(Robot).filter(
            Robot.account == robot_channel,
            Robot.enable == 1
        ).first()
        if not robot:
            return

        logging.info(f"处理新成员入群事件: {NewMemberInfo}")
        # 查询群组（必须启用）
        group = db.query(Group).filter(
            Group.number == str(NewMemberInfo["group_id"]),
            Group.listening == True
        ).first()
        if not group:
            return
        # 检查该机器人是否绑定到该群组
        group_robot_exists = db.query(GroupRobot).filter(
            GroupRobot.group_id == group.id,
            GroupRobot.robot_id == robot.id
        ).first()
        if not group_robot_exists:
            return
        # 获取欢迎语脚本
        scripter = db.query(Script).filter(
            Script.environment == "入群介绍语"
        ).first()
        if not scripter:
            return

        buildContent = str(scripter.content).replace("{机器人昵称}", str(robot.nickname))

        # 添加新成员记录
        newMember = GroupMember(
            number=NewMemberInfo["user_id"],
            name=NewMemberInfo["nickname"],
            group_id=group.id,
            permission=0,
            role='regular'
        )
        db.add(newMember)

        # 创建消息包和存档
        packed = MessagePacket(
            sender=robot.nickname,
            receiver=str(NewMemberInfo["user_id"]),  # 转成字符串
            content=buildContent,
            position=str(NewMemberInfo["group_id"]),  # 转成字符串
            type=1,
            channel=-1
        )
        convert = Message(
            sender=robot.nickname,
            receiver=NewMemberInfo["nickname"],
            content=packed.content,
            position=str(packed.position),
            type=packed.type,
            sequence=-1
        )
        await qq_websocket_channel.send_message_to_robot(
            robot=robot_channel,
            message=command.AtSendPayload(packed)
        )
        db.add(convert)
        db.commit()
        logging.info(
            f"机器人 {robot.nickname} 接收到来自QQ群 {convert.position} 的新成员 {NewMemberInfo['nickname']} 入群信息")
    except Exception as e:
        print(f"异常发生: {e}")
        db.rollback()
    finally:
        db.close()


async def processMessageWithdrawnQQ(MessageInfo: dict, robot_channel: str):
    """处理QQ消息撤回事件。

    当群消息被撤回时，根据消息序号和群号查找原始消息并进行相应处理。

    Args:
        MessageInfo (dict): 被撤回的消息信息，包含以下字段：
            - sequence: 消息序号
            - group_id: 群号
        robot_channel (str): 机器人通道标识

    Raises:
        Exception: 数据库操作异常
    """
    db_gen = get_db()
    db: Session = next(db_gen)

    try:
        origin = db.query(Message).filter(
            Message.sequence == MessageInfo["sequence"],
            Message.position == MessageInfo["group_id"]
        ).first()

        if not origin:
            return

        print(f"检测到撤回：seq:{MessageInfo['sequence']}于{MessageInfo['group_id']}")

        relatedTask = db.query(Task).filter(Task.message_id == origin.id).first()
        if relatedTask:

            instructions = db.query(Instruction).filter(Instruction.task_id == relatedTask.id).all()
            instruction_ids = [i.id for i in instructions]

            if instruction_ids:

                audits = db.query(Audit).filter(Audit.instruction_id.in_(instruction_ids)).all()
                for each in audits:
                    print(f"audit条目撤回-{each.id}，original status:{each.status}")
                    # 使用新的状态管理方式：添加撤回标志
                    base_status = each.status % 10  # 保留基础状态位
                    other_flags = each.status - (each.status % 100)  # 保留100位以上的标志
                    each.status = other_flags + AuditStatus.WITHDRAWN + base_status
                    print(f"audit条目撤回-{each.id}，new status:{each.status}")
                    db.commit()

            else:
                db.delete(relatedTask)
                db.commit()

    except Exception as e:
        db.rollback()
    finally:
        db.close()


from sqlalchemy.orm.attributes import flag_modified


async def processGroupMemberUpdateQQ(data: list, robot_channel: str):
    """处理QQ群成员信息更新事件，包括成员新增、更新、删除。"""

    db_gen = get_db()
    db: Session = next(db_gen)

    if not data:
        return

    try:
        group_number = str(data[0].get("group_id"))
        group = db.query(Group).filter(Group.number == group_number, Group.type == 'QQ').first()
        if not group:
            logging.warning(f"未找到群号为 {group_number} 的QQ群信息")
            return

        robots = group.robot or []

        # 提取当前上报的所有成员编号
        incoming_numbers = {str(m.get('number')) for m in data if m.get('number')}
        incoming_map = {str(m['number']): m for m in data if 'number' in m}

        # 查询已有成员
        existing_members = db.query(GroupMember).filter(GroupMember.group_id == group.id).all()
        existing_map = {m.number: m for m in existing_members}
        existing_numbers = set(existing_map.keys())

        # 查询全部 QQ 用户（trader）和 QQ 机器人（robot）
        user_set = {u.qq for u in db.query(User.qq).filter(User.role == UserRole.salesman, User.qq != None).all()}

        print(f"系统员工：{user_set}")

        robot_map = {r.account: r.id for r in db.query(Robot).filter(Robot.type == 'QQ').all()}

        # 1. 删除已退群成员（数据库中存在，但不在上报数据里）
        to_remove_numbers = existing_numbers - incoming_numbers
        if to_remove_numbers:
            db.query(GroupMember).filter(
                GroupMember.group_id == group.id,
                GroupMember.number.in_(to_remove_numbers)
            ).delete(synchronize_session=False)
            db.flush()
            logging.info(f"已删除 {len(to_remove_numbers)} 个退群成员")

        # 2. 处理新增和更新成员
        for number in incoming_numbers:
            members = incoming_map[number]
            name = members.get('name', '').strip()

            # 默认角色
            member_role = 'regular'

            # 是否为机器人
            robot_id = robot_map.get(number)
            if robot_id:
                member_role = 'robot'
                if robot_id not in robots:
                    robots.append(robot_id)
                    flag_modified(group, "robot")
            elif number in user_set:
                member_role = 'trader'

            if number in existing_map:
                # 更新已有成员
                existing_member = existing_map[number]
                existing_member.name = name
                existing_member.role = member_role
            else:
                # 插入新成员
                new_member = GroupMember(
                    number=number,
                    name=name,
                    group_id=group.id,
                    permission=0,
                    role=member_role
                )
                db.add(new_member)

        # 更新 group 中的机器人列表
        group.robot = robots
        db.commit()
        logging.info(f"已更新群[{group.name}]成员信息：当前成员 {len(incoming_numbers)}，删除 {len(to_remove_numbers)}")

    except Exception as e:
        logging.error(f"更新群[{data[0].get('group_id')}]信息失败: {str(e)}\n{traceback.format_exc()}")
        db.rollback()
    finally:
        db.close()


async def processGroupMemberDecreasedQQ(DeletedMemberInfo: dict, robot_channel: str):
    """处理QQ群成员减少事件。

    当成员退出或被移出群时，更新数据库中的成员记录。

    Args:
        DeletedMemberInfo (dict): 离开成员的信息，包含：
            - user_id: 用户QQ号
            - group_id: 群号
        robot_channel (str): 机器人通道标识

    Raises:
        Exception: 数据库操作异常
    """
    db_gen = get_db()
    db: Session = next(db_gen)
    try:
        db.query(GroupMember).filter(GroupMember.group_id == DeletedMemberInfo["group_id"],
                                     GroupMember.number == DeletedMemberInfo["user_id"]).delete()
        db.commit()
    except Exception as e:
        db.rollback()
        logging.error(str(e))
    finally:
        db.close()


async def processOfflineQQ(data: dict, robot_channel: str):
    """处理QQ机器人离线事件。

    当机器人离线时更新其在数据库中的状态。

    Args:
        data (dict): 离线事件数据
        robot_channel (str): 机器人通道标识

    Raises:
        Exception: 数据库操作异常
    """
    db_gen = get_db()
    db: Session = next(db_gen)
    try:
        robot = db.query(Robot).filter(Robot.account == robot_channel).first()
        robot.status = False
        db.commit()
    except Exception as e:
        db.rollback()
        logging.error(str(e))
    finally:
        db.close()


async def processAuditReply(data: Message):
    """处理审核回复消息。

    处理对审核项的回复消息，包括消息发送和状态更新。
    根据消息内容更新相关审核记录。

    Args:
        data (Message): 回复消息对象，包含回复内容和相关审核信息

    Raises:
        Exception: 数据库操作异常或消息发送失败
    """
    packed = MessagePacket(
        sender=data.sender,
        receiver=data.receiver,
        content=data.content,
        position=data.position,
        type=data.type,
        reply=1,
        channel=data.channel,
        sequence=data.sequence,
    )
    payload = command.AtSendWIthReplyPayload(packed)
    await qq_websocket_channel.send_message_to_robot(packed.channel, payload)


async def processOnlineQQ(data: dict, robot_channel: str):
    """处理QQ机器人上线事件（仅在状态非True时更新）

    Args:
        data (dict): 上线事件数据
        robot_channel (str): 机器人通道标识

    Raises:
        Exception: 数据库操作异常
    """
    db_gen = get_db()
    db: Session = next(db_gen)
    try:
        # 查询机器人当前状态
        robot = db.query(Robot).filter(Robot.account == robot_channel).first()

        if not robot:
            logging.warning(f"机器人账号 {robot_channel} 不存在！")
            return

        # 仅当状态不为 True 时才更新
        if not robot.status:  # 或者 if robot.status is not True
            logging.info(f"机器人 {robot.nickname} 上线，状态从 [离线] 更新为 [在线]")
            robot.status = True
            db.commit()
        else:
            logging.debug(f"机器人 {robot.nickname} 已经在线，无需重复更新")

    except Exception as e:
        db.rollback()
        logging.error(f"更新机器人状态失败: {str(e)}")
        raise  # 可选：是否重新抛出异常取决于业务需求
    finally:
        db.close()


async def processPrivateMessageQQ(data: dict, robot_channel: str):
    db_gen = get_db()
    db: Session = next(db_gen)
    try:
        content = db.query(Script).filter(Script.environment == '私聊拒绝话术').first().content
        await qq_websocket_channel.send_message_to_robot(robot=robot_channel,
                                                         message=command.SendPrivateMessage(data['position'], content))

    except Exception as e:
        logging.error(str(e))
    finally:
        db.close()
