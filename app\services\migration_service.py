#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
数据迁移服务模块

提供数据迁移的调度和管理功能
"""

import asyncio
import subprocess
import logging
import json
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from app.models.models import Log
from app.core.database import get_db

class MigrationService:
    """数据迁移服务"""
    
    def __init__(self):
        self.migration_tables = [
            'bb_ttraderivallinkman',
            'bb_ttraderival', 
            'bb_thgregister',
            'bb_tfundinfo'
        ]
        self.batch_size = 100000
        self.results_file = 'migration_results.json'
        self.running_tasks = {}  # 跟踪正在运行的任务
        
    async def run_migration(self, table_name: str, db: Session, operator: str = "system") -> Dict[str, Any]:
        """执行单个表的迁移"""
        start_time = datetime.now()
        
        # 记录开始日志
        log = Log(
            operator=operator,
            target=table_name,
            action="数据迁移",
            object="数据表",
            result=False,
        )
        
        cmd = [
            'python', 'migration.py',
            '--source-table', table_name,
            '--target-table', table_name,
            '--batch-size', str(self.batch_size)
        ]
        
        logging.info(f"开始迁移表: {table_name}")
        
        try:
            # 修复编码问题 - 使用 gbk 编码处理中文输出
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='gbk',  # 改为 gbk 编码
                errors='ignore',  # 忽略编码错误
                timeout=3600  # 1小时超时
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            migration_result = {
                'table': table_name,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration_seconds': duration,
                'success': result.returncode == 0,
                'stdout': result.stdout if result.stdout else "",
                'stderr': result.stderr if result.stderr else "",
                'return_code': result.returncode
            }
            
            # 更新日志
            log.result = result.returncode == 0
            if result.returncode == 0:
                log.clause = f"耗时 {duration:.2f} 秒"
            else:
                # 安全处理错误信息
                error_msg = result.stderr if result.stderr else "未知错误"
                log.clause = error_msg[:500]
            
            if result.returncode == 0:
                logging.info(f"表 {table_name} 迁移成功，耗时 {duration:.2f} 秒")
            else:
                logging.error(f"表 {table_name} 迁移失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            migration_result = {
                'table': table_name,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration_seconds': duration,
                'success': False,
                'error': 'Migration timeout after 1 hour',
                'return_code': -1
            }
            
            log.result = False
            log.clause = "迁移超时"
            logging.error(f"表 {table_name} 迁移超时")
            
        except UnicodeDecodeError as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # 尝试用不同编码重新执行
            try:
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    errors='replace',  # 替换无法解码的字符
                    timeout=3600
                )
                
                migration_result = {
                    'table': table_name,
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat(),
                    'duration_seconds': duration,
                    'success': result.returncode == 0,
                    'stdout': result.stdout if result.stdout else "",
                    'stderr': result.stderr if result.stderr else "",
                    'return_code': result.returncode,
                    'encoding_warning': '输出包含编码问题，已自动处理'
                }
                
                log.result = result.returncode == 0
                log.clause = f"编码问题已处理，{'成功' if result.returncode == 0 else '失败'}"
                
            except Exception:
                migration_result = {
                    'table': table_name,
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat(),
                    'duration_seconds': duration,
                    'success': False,
                    'error': f'编码错误: {str(e)}',
                    'return_code': -1
                }
                
                log.result = False
                log.clause = f"编码错误: {str(e)[:200]}"
            
            logging.error(f"表 {table_name} 迁移编码错误: {str(e)}")
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            migration_result = {
                'table': table_name,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration_seconds': duration,
                'success': False,
                'error': str(e),
                'return_code': -1
            }
            
            log.result = False
            log.clause = str(e)[:500]
            logging.error(f"表 {table_name} 迁移异常: {str(e)}")
        
        # 保存日志
        try:
            db.add(log)
            db.commit()
        except Exception as e:
            logging.error(f"保存迁移日志失败: {str(e)}")
            db.rollback()
            
        return migration_result
    
    def start_migration_task(self, operator: str = "system") -> str:
        """启动迁移任务并返回任务ID"""
        job_id = f"migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 创建异步任务
        task = asyncio.create_task(
            self.run_all_migrations_async(operator)
        )
        
        # 添加任务完成回调
        task.add_done_callback(self._task_done_callback)
        
        return job_id

    def _task_done_callback(self, task):
        """任务完成回调"""
        try:
            result = task.result()
            logging.info(f"迁移任务完成: {result.get('job_id', 'unknown')}")
        except Exception as e:
            logging.error(f"迁移任务异常: {str(e)}")

    def get_task_status(self, job_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        return self.running_tasks.get(job_id, {'status': 'not_found'})

    async def run_all_migrations_async(self, operator: str = "system") -> Dict[str, Any]:
        """异步执行所有表的迁移"""
        job_start_time = datetime.now()
        job_id = f"migration_{job_start_time.strftime('%Y%m%d_%H%M%S')}"
        
        logging.info(f"开始执行数据迁移任务: {job_id}")
        
        # 标记任务开始
        self.running_tasks[job_id] = {
            'status': 'running',
            'start_time': job_start_time,
            'operator': operator,
            'tables': self.migration_tables.copy()
        }
        
        results = []
        success_count = 0
        
        # 获取新的数据库会话
        from app.core.database import get_db
        db_gen = get_db()
        db: Session = next(db_gen)
        
        try:
            for table in self.migration_tables:
                result = await self.run_migration_async(table, db, operator)
                results.append(result)
                
                if result['success']:
                    success_count += 1
            
            job_end_time = datetime.now()
            total_duration = (job_end_time - job_start_time).total_seconds()
            
            # 汇总结果
            summary = {
                'job_id': job_id,
                'job_start_time': job_start_time.isoformat(),
                'job_end_time': job_end_time.isoformat(),
                'total_duration_seconds': total_duration,
                'total_tables': len(self.migration_tables),
                'success_count': success_count,
                'failed_count': len(self.migration_tables) - success_count,
                'operator': operator,
                'results': results
            }
            
            # 保存结果
            self.save_results(summary)
            
            # 更新任务状态
            self.running_tasks[job_id]['status'] = 'completed'
            self.running_tasks[job_id]['summary'] = summary
            
            logging.info(f"数据迁移任务完成: {job_id}, 成功 {success_count}/{len(self.migration_tables)}")
            
            return summary
            
        except Exception as e:
            logging.error(f"迁移任务异常: {str(e)}")
            self.running_tasks[job_id]['status'] = 'failed'
            self.running_tasks[job_id]['error'] = str(e)
            raise
        finally:
            db.close()

    async def run_migration_async(self, table_name: str, db: Session, operator: str = "system") -> Dict[str, Any]:
        """异步执行单个表的迁移"""
        start_time = datetime.now()
        
        # 记录开始日志
        log = Log(
            operator=operator,
            target=table_name,
            action="数据迁移",
            object="数据表",
            result=False,
        )
        
        cmd = [
            'python', 'migration.py',
            '--source-table', table_name,
            '--target-table', table_name,
            '--batch-size', str(self.batch_size)
        ]
        
        logging.info(f"开始迁移表: {table_name}")
        
        try:
            # 使用 asyncio.create_subprocess_exec 异步执行（不指定encoding）
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            # 异步等待进程完成
            stdout_bytes, stderr_bytes = await asyncio.wait_for(
                process.communicate(), 
                timeout=3600  # 1小时超时
            )
            
            # 手动处理编码
            try:
                stdout = stdout_bytes.decode('gbk', errors='ignore') if stdout_bytes else ""
                stderr = stderr_bytes.decode('gbk', errors='ignore') if stderr_bytes else ""
            except UnicodeDecodeError:
                try:
                    stdout = stdout_bytes.decode('utf-8', errors='replace') if stdout_bytes else ""
                    stderr = stderr_bytes.decode('utf-8', errors='replace') if stderr_bytes else ""
                except:
                    stdout = str(stdout_bytes) if stdout_bytes else ""
                    stderr = str(stderr_bytes) if stderr_bytes else ""
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            migration_result = {
                'table': table_name,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration_seconds': duration,
                'success': process.returncode == 0,
                'stdout': stdout,
                'stderr': stderr,
                'return_code': process.returncode
            }
            
            # 更新日志
            log.result = process.returncode == 0
            if process.returncode == 0:
                log.clause = f"耗时 {duration:.2f} 秒"
            else:
                error_msg = stderr if stderr else "未知错误"
                log.clause = error_msg[:500]
            
            if process.returncode == 0:
                logging.info(f"表 {table_name} 迁移成功，耗时 {duration:.2f} 秒")
            else:
                logging.error(f"表 {table_name} 迁移失败: {stderr}")
                
        except asyncio.TimeoutError:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            migration_result = {
                'table': table_name,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration_seconds': duration,
                'success': False,
                'error': 'Migration timeout after 1 hour',
                'return_code': -1
            }
            
            log.result = False
            log.clause = "迁移超时"
            logging.error(f"表 {table_name} 迁移超时")
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            migration_result = {
                'table': table_name,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration_seconds': duration,
                'success': False,
                'error': str(e),
                'return_code': -1
            }
            
            log.result = False
            log.clause = str(e)[:500]
            logging.error(f"表 {table_name} 迁移异常: {str(e)}")
        
        # 保存日志
        try:
            db.add(log)
            db.commit()
        except Exception as e:
            logging.error(f"保存迁移日志失败: {str(e)}")
            db.rollback()
            
        return migration_result
    
    def save_results(self, summary: Dict[str, Any]):
        """保存迁移结果到文件"""
        try:
            if os.path.exists(self.results_file):
                with open(self.results_file, 'r', encoding='utf-8') as f:
                    all_results = json.load(f)
            else:
                all_results = []
            
            all_results.append(summary)
            
            # 只保留最近30天的结果
            if len(all_results) > 30:
                all_results = all_results[-30:]
            
            with open(self.results_file, 'w', encoding='utf-8') as f:
                json.dump(all_results, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logging.error(f"保存结果失败: {str(e)}")
    
    def get_migration_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取迁移历史记录"""
        try:
            if os.path.exists(self.results_file):
                with open(self.results_file, 'r', encoding='utf-8') as f:
                    all_results = json.load(f)
                return all_results[-limit:] if all_results else []
            return []
        except Exception as e:
            logging.error(f"读取迁移历史失败: {str(e)}")
            return []

# 全局实例
migration_service = MigrationService()







