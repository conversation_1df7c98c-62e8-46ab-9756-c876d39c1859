#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2019 Tianxiadatong, Co.,Ltd. All Rights Reserved.

This module implements the FastAPI sub-router for the /script endpoint,
including related business logic.

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06
"""
import logging

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from fastapi import Request

from app.assets.public_assets import general_return_json
from app.core.database import get_db
from app.decorator.auth import required_login
from app.models.models import Script
from app.schemas.schemas import ScriptBase

router = APIRouter(prefix="/script")

#获取所有话术
@router.get("/", summary="获取所有话术")
@required_login(roles=["admin","salesman", "super_admin"])
async def get_all_script(request: Request, db: Session=Depends(get_db)):
    """获取所有话术列表。

    Args:
        request (Request): FastAPI 请求对象
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 返回话术列表

    Raises:
        HTTPException: 数据库操作异常
    """
    # 根据 id 排序获取所有话术
    try:
        script = db.query(Script).order_by(Script.id).all()
        return general_return_json(200, "ok", script)
    except Exception as e:
        logging.error(f"获取话术出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取出错: {str(e)}")


#修改话术
@router.post("/modify", summary="改话术，改enable和content内容")
@required_login(roles=["admin", "super_admin"])
async def modify_script(request: Request, script: ScriptBase, db: Session=Depends(get_db)):
    """修改话术内容和启用状态。

    Args:
        request (Request): FastAPI 请求对象
        script (ScriptBase): 话术修改参数
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 修改成功

    Raises:
        HTTPException: 数据库操作异常
    """
    try:
        selected_script = db.query(Script).filter(Script.id == script.id).first()
        selected_script.content = script.content
        selected_script.enable = script.enable
        db.commit()
        logging.info(f"修改 {selected_script.environment} 话术为 {selected_script.content}")
        return general_return_json(200, "ok")
    except Exception as e:
        db.rollback()
        logging.error(f"修改话术出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"修改出错: {str(e)}")