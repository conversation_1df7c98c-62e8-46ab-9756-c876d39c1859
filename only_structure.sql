/*
 Navicat Premium Dump SQL

 Source Server         : localhost_5432
 Source Server Type    : PostgreSQL
 Source Server Version : 120001 (120001)
 Source Host           : localhost:54321
 Source Catalog        : robot
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 120001 (120001)
 File Encoding         : 65001

 Date: 08/07/2025 16:12:30
*/


-- ----------------------------
-- Type structure for group_type
-- ----------------------------
DROP TYPE IF EXISTS "public"."group_type";
CREATE TYPE "public"."group_type" AS ENUM (
  'QQ',
  '微信'
);

-- ----------------------------
-- Type structure for market_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."market_enum";
CREATE TYPE "public"."market_enum" AS ENUM (
  '银行间',
  '上海',
  '深圳'
);

-- ----------------------------
-- Type structure for robot_type
-- ----------------------------
DROP TYPE IF EXISTS "public"."robot_type";
CREATE TYPE "public"."robot_type" AS ENUM (
  '微信',
  'QQ'
);

-- ----------------------------
-- Type structure for user_role
-- ----------------------------
DROP TYPE IF EXISTS "public"."user_role";
CREATE TYPE "public"."user_role" AS ENUM (
  'salesman',
  'admin',
  'root'
);

-- ----------------------------
-- Type structure for variety_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."variety_enum";
CREATE TYPE "public"."variety_enum" AS ENUM (
  '现券',
  '回购'
);

-- ----------------------------
-- Sequence structure for audit_id
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."audit_id";
CREATE SEQUENCE "public"."audit_id" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for audit_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."audit_id_seq";
CREATE SEQUENCE "public"."audit_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for audit_id_seq1
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."audit_id_seq1";
CREATE SEQUENCE "public"."audit_id_seq1" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for consigner_id
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."consigner_id";
CREATE SEQUENCE "public"."consigner_id" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for consigner_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."consigner_id_seq";
CREATE SEQUENCE "public"."consigner_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for consigner_id_seq1
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."consigner_id_seq1";
CREATE SEQUENCE "public"."consigner_id_seq1" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for group_consigner_id
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."group_consigner_id";
CREATE SEQUENCE "public"."group_consigner_id" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for group_consigner_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."group_consigner_id_seq";
CREATE SEQUENCE "public"."group_consigner_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for group_id
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."group_id";
CREATE SEQUENCE "public"."group_id" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for group_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."group_id_seq";
CREATE SEQUENCE "public"."group_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for group_id_seq1
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."group_id_seq1";
CREATE SEQUENCE "public"."group_id_seq1" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for group_member_id
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."group_member_id";
CREATE SEQUENCE "public"."group_member_id" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for group_member_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."group_member_id_seq";
CREATE SEQUENCE "public"."group_member_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for group_member_id_seq1
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."group_member_id_seq1";
CREATE SEQUENCE "public"."group_member_id_seq1" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for group_production_id
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."group_production_id";
CREATE SEQUENCE "public"."group_production_id" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for group_production_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."group_production_id_seq";
CREATE SEQUENCE "public"."group_production_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for group_production_id_seq1
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."group_production_id_seq1";
CREATE SEQUENCE "public"."group_production_id_seq1" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for group_robot_id
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."group_robot_id";
CREATE SEQUENCE "public"."group_robot_id" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for group_robot_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."group_robot_id_seq";
CREATE SEQUENCE "public"."group_robot_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for group_robot_id_seq1
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."group_robot_id_seq1";
CREATE SEQUENCE "public"."group_robot_id_seq1" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for group_trader_id
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."group_trader_id";
CREATE SEQUENCE "public"."group_trader_id" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for group_trader_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."group_trader_id_seq";
CREATE SEQUENCE "public"."group_trader_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for group_trader_id_seq1
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."group_trader_id_seq1";
CREATE SEQUENCE "public"."group_trader_id_seq1" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for id
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."id";
CREATE SEQUENCE "public"."id" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for instruction_id
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."instruction_id";
CREATE SEQUENCE "public"."instruction_id" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for instruction_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."instruction_id_seq";
CREATE SEQUENCE "public"."instruction_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for instruction_id_seq1
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."instruction_id_seq1";
CREATE SEQUENCE "public"."instruction_id_seq1" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for log_id
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."log_id";
CREATE SEQUENCE "public"."log_id" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for log_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."log_id_seq";
CREATE SEQUENCE "public"."log_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for log_id_seq1
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."log_id_seq1";
CREATE SEQUENCE "public"."log_id_seq1" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for message_id
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."message_id";
CREATE SEQUENCE "public"."message_id" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for message_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."message_id_seq";
CREATE SEQUENCE "public"."message_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for message_id_seq1
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."message_id_seq1";
CREATE SEQUENCE "public"."message_id_seq1" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for production_id
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."production_id";
CREATE SEQUENCE "public"."production_id" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for production_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."production_id_seq";
CREATE SEQUENCE "public"."production_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for production_id_seq1
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."production_id_seq1";
CREATE SEQUENCE "public"."production_id_seq1" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for robot_id
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."robot_id";
CREATE SEQUENCE "public"."robot_id" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for robot_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."robot_id_seq";
CREATE SEQUENCE "public"."robot_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for robot_id_seq1
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."robot_id_seq1";
CREATE SEQUENCE "public"."robot_id_seq1" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for script_id
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."script_id";
CREATE SEQUENCE "public"."script_id" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for script_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."script_id_seq";
CREATE SEQUENCE "public"."script_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for script_id_seq1
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."script_id_seq1";
CREATE SEQUENCE "public"."script_id_seq1" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for task_id
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."task_id";
CREATE SEQUENCE "public"."task_id" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for task_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."task_id_seq";
CREATE SEQUENCE "public"."task_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for task_id_seq1
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."task_id_seq1";
CREATE SEQUENCE "public"."task_id_seq1" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for user_id
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."user_id";
CREATE SEQUENCE "public"."user_id" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for user_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."user_id_seq";
CREATE SEQUENCE "public"."user_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for user_id_seq1
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."user_id_seq1";
CREATE SEQUENCE "public"."user_id_seq1" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Table structure for audit
-- ----------------------------
DROP TABLE IF EXISTS "public"."audit";
CREATE TABLE "public"."audit" (
  "id" int4 NOT NULL DEFAULT nextval('audit_id'::regclass),
  "variety" "public"."variety_enum" NOT NULL,
  "market" "public"."market_enum" NOT NULL,
  "classification" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "execution" timestamp(6) NOT NULL,
  "production" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "direction" int2 NOT NULL,
  "three_party_sequel" varchar(255) COLLATE "pg_catalog"."default",
  "security_code" varchar(100) COLLATE "pg_catalog"."default",
  "security_name" varchar(255) COLLATE "pg_catalog"."default",
  "yield_rate" varchar(200) COLLATE "pg_catalog"."default",
  "net_price" varchar(100) COLLATE "pg_catalog"."default",
  "full_price" varchar(100) COLLATE "pg_catalog"."default",
  "yield_to_maturity" varchar(100) COLLATE "pg_catalog"."default",
  "option_yield" varchar(100) COLLATE "pg_catalog"."default",
  "quantity" varchar(50) COLLATE "pg_catalog"."default",
  "denomination" varchar(150) COLLATE "pg_catalog"."default",
  "liquidation_speed" varchar(200) COLLATE "pg_catalog"."default",
  "rival" varchar(255) COLLATE "pg_catalog"."default",
  "rival_trader" varchar(200) COLLATE "pg_catalog"."default",
  "rival_seat" varchar(200) COLLATE "pg_catalog"."default",
  "rival_trader_id" varchar(200) COLLATE "pg_catalog"."default",
  "agreement_number" varchar(200) COLLATE "pg_catalog"."default",
  "payment_method" varchar(200) COLLATE "pg_catalog"."default",
  "declaration_type" varchar(200) COLLATE "pg_catalog"."default",
  "rival_dealer_code" varchar(200) COLLATE "pg_catalog"."default",
  "trader_entity_code" varchar(150) COLLATE "pg_catalog"."default",
  "rival_trader_code" varchar(150) COLLATE "pg_catalog"."default",
  "deadline" varchar(100) COLLATE "pg_catalog"."default",
  "violation_grace_period" varchar(150) COLLATE "pg_catalog"."default",
  "supplementary_term" text COLLATE "pg_catalog"."default",
  "share_bonus_method" varchar(200) COLLATE "pg_catalog"."default",
  "distribution_channel" varchar(200) COLLATE "pg_catalog"."default",
  "pledge_coupon_code" varchar(150) COLLATE "pg_catalog"."default",
  "pledge_coupon_name" varchar(200) COLLATE "pg_catalog"."default",
  "pledge_quantity" varchar(150) COLLATE "pg_catalog"."default",
  "discount_rate" varchar(100) COLLATE "pg_catalog"."default",
  "message" int4 NOT NULL,
  "instruction_id" int4 NOT NULL,
  "status" int2 NOT NULL,
  "auditor_id" int4,
  "production_id" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."audit"."variety" IS '品种';
COMMENT ON COLUMN "public"."audit"."market" IS '市场';
COMMENT ON COLUMN "public"."audit"."execution" IS '执行日';
COMMENT ON COLUMN "public"."audit"."production" IS '产品名称';
COMMENT ON COLUMN "public"."audit"."direction" IS '方向（买入买出）';
COMMENT ON COLUMN "public"."audit"."three_party_sequel" IS '三方续作';
COMMENT ON COLUMN "public"."audit"."security_code" IS '证券代码';
COMMENT ON COLUMN "public"."audit"."security_name" IS '证券名';
COMMENT ON COLUMN "public"."audit"."yield_rate" IS '收益率';
COMMENT ON COLUMN "public"."audit"."net_price" IS '净价/委托价格';
COMMENT ON COLUMN "public"."audit"."full_price" IS '全价';
COMMENT ON COLUMN "public"."audit"."yield_to_maturity" IS '到期收益率';
COMMENT ON COLUMN "public"."audit"."option_yield" IS '行权收益率';
COMMENT ON COLUMN "public"."audit"."quantity" IS '数量';
COMMENT ON COLUMN "public"."audit"."denomination" IS '金额/面额';
COMMENT ON COLUMN "public"."audit"."liquidation_speed" IS '清算速度';
COMMENT ON COLUMN "public"."audit"."rival" IS '对手方';
COMMENT ON COLUMN "public"."audit"."rival_trader" IS '对手方交易员';
COMMENT ON COLUMN "public"."audit"."rival_seat" IS '对手席位';
COMMENT ON COLUMN "public"."audit"."rival_trader_id" IS '对手交易员号';
COMMENT ON COLUMN "public"."audit"."agreement_number" IS '约定号';
COMMENT ON COLUMN "public"."audit"."payment_method" IS '结算方式';
COMMENT ON COLUMN "public"."audit"."declaration_type" IS '申报类型';
COMMENT ON COLUMN "public"."audit"."rival_dealer_code" IS '对手交易商代码';
COMMENT ON COLUMN "public"."audit"."trader_entity_code" IS '交易商主体代码';
COMMENT ON COLUMN "public"."audit"."rival_trader_code" IS '对手交易员代码';
COMMENT ON COLUMN "public"."audit"."deadline" IS '期限';
COMMENT ON COLUMN "public"."audit"."violation_grace_period" IS '违约宽限期';
COMMENT ON COLUMN "public"."audit"."supplementary_term" IS '补充条款';
COMMENT ON COLUMN "public"."audit"."share_bonus_method" IS '分红方式';
COMMENT ON COLUMN "public"."audit"."distribution_channel" IS '销售渠道';
COMMENT ON COLUMN "public"."audit"."pledge_coupon_code" IS '质押券代码';
COMMENT ON COLUMN "public"."audit"."pledge_coupon_name" IS '质押券名称';
COMMENT ON COLUMN "public"."audit"."pledge_quantity" IS '质押数量';
COMMENT ON COLUMN "public"."audit"."discount_rate" IS '折扣率';
COMMENT ON COLUMN "public"."audit"."message" IS '原始消息';
COMMENT ON COLUMN "public"."audit"."instruction_id" IS '指令ID';
COMMENT ON COLUMN "public"."audit"."status" IS '未处理/已撤回(未处理)/已变更(未处理)/已处理(失效)/已处理(确认)';
COMMENT ON COLUMN "public"."audit"."auditor_id" IS '审核人';
COMMENT ON COLUMN "public"."audit"."production_id" IS '产品ID';
COMMENT ON TABLE "public"."audit" IS '人工审核表';

-- ----------------------------
-- Table structure for consigner
-- ----------------------------
DROP TABLE IF EXISTS "public"."consigner";
CREATE TABLE "public"."consigner" (
  "id" int4 NOT NULL DEFAULT nextval('group_id'::regclass),
  "consigner" varchar(200) COLLATE "pg_catalog"."default" NOT NULL
)
;

-- ----------------------------
-- Table structure for group
-- ----------------------------
DROP TABLE IF EXISTS "public"."group";
CREATE TABLE "public"."group" (
  "id" int4 NOT NULL DEFAULT nextval('group_id_seq1'::regclass),
  "type" "public"."group_type" NOT NULL,
  "name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "number" varchar(200) COLLATE "pg_catalog"."default",
  "listening" bool NOT NULL,
  "robot" json NOT NULL
)
;
COMMENT ON COLUMN "public"."group"."number" IS '群号';
COMMENT ON COLUMN "public"."group"."listening" IS '是否启用（即监听）';

-- ----------------------------
-- Table structure for group_consigner
-- ----------------------------
DROP TABLE IF EXISTS "public"."group_consigner";
CREATE TABLE "public"."group_consigner" (
  "id" int4 NOT NULL DEFAULT nextval('group_consigner_id'::regclass),
  "group_id" int4 NOT NULL,
  "consigner_id" int4 NOT NULL
)
;

-- ----------------------------
-- Table structure for group_member
-- ----------------------------
DROP TABLE IF EXISTS "public"."group_member";
CREATE TABLE "public"."group_member" (
  "id" int4 NOT NULL DEFAULT nextval('group_member_id'::regclass),
  "number" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "group_id" int4 NOT NULL,
  "permission" int2 NOT NULL,
  "role" varchar(20) NOT NULL DEFAULT 'regular'
)
;

-- ----------------------------
-- Table structure for group_production
-- ----------------------------
DROP TABLE IF EXISTS "public"."group_production";
CREATE TABLE "public"."group_production" (
  "id" int4 NOT NULL DEFAULT nextval('group_production_id'::regclass),
  "group_id" int4 NOT NULL,
  "production_id" int4 NOT NULL
)
;

-- ----------------------------
-- Table structure for group_robot
-- ----------------------------
DROP TABLE IF EXISTS "public"."group_robot";
CREATE TABLE "public"."group_robot" (
  "id" int4 NOT NULL DEFAULT nextval('group_robot_id'::regclass),
  "robot_id" int4 NOT NULL,
  "group_id" int4 NOT NULL
)
;

-- ----------------------------
-- Table structure for group_trader
-- ----------------------------
DROP TABLE IF EXISTS "public"."group_trader";
CREATE TABLE "public"."group_trader" (
  "id" int4 NOT NULL DEFAULT nextval('group_trader_id'::regclass),
  "group_id" int4 NOT NULL,
  "trader_id" int4 NOT NULL
)
;

-- ----------------------------
-- Table structure for instruction
-- ----------------------------
DROP TABLE IF EXISTS "public"."instruction";
CREATE TABLE "public"."instruction" (
  "id" int4 NOT NULL DEFAULT nextval('instruction_id'::regclass),
  "task_id" int4 NOT NULL
)
;

-- ----------------------------
-- Table structure for log
-- ----------------------------
DROP TABLE IF EXISTS "public"."log";
CREATE TABLE "public"."log" (
  "id" int4 NOT NULL DEFAULT nextval('log_id'::regclass),
  "operator" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "target" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "action" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "result" bool NOT NULL,
  "clause" text COLLATE "pg_catalog"."default",
  "date" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP
)
;

-- ----------------------------
-- Table structure for message
-- ----------------------------
DROP TABLE IF EXISTS "public"."message";
CREATE TABLE "public"."message" (
  "id" int4 NOT NULL DEFAULT nextval('message_id'::regclass),
  "sender" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "receiver" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "content" text COLLATE "pg_catalog"."default" NOT NULL,
  "position" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "type" int2 NOT NULL,
  "reply" int4,
  "channel" varchar(255) COLLATE "pg_catalog"."default",
  "sequence" int8,
  "date" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP
)
;
COMMENT ON COLUMN "public"."message"."sender" IS '发送者';
COMMENT ON COLUMN "public"."message"."receiver" IS '接收者';
COMMENT ON COLUMN "public"."message"."content" IS '内容';
COMMENT ON COLUMN "public"."message"."position" IS '哪个群发的/还是私聊发的';
COMMENT ON COLUMN "public"."message"."type" IS '什么类型的消息?Text/image?是不是在回复别人？';
COMMENT ON COLUMN "public"."message"."reply" IS '回复哪条消息';
COMMENT ON COLUMN "public"."message"."sequence" IS '唯一信息编号（机器人端用）';
COMMENT ON COLUMN "public"."message"."date" IS '消息时间';

-- ----------------------------
-- Table structure for production
-- ----------------------------
DROP TABLE IF EXISTS "public"."production";
CREATE TABLE "public"."production" (
  "id" int4 NOT NULL DEFAULT nextval('production_id'::regclass),
  "production_code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "production" varchar(255) COLLATE "pg_catalog"."default" NOT NULL
)
;

-- ----------------------------
-- Table structure for robot
-- ----------------------------
DROP TABLE IF EXISTS "public"."robot";
CREATE TABLE "public"."robot" (
  "id" int4 NOT NULL DEFAULT nextval('robot_id'::regclass),
  "type" "public"."robot_type" NOT NULL,
  "uni" int4 NOT NULL,
  "account" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "gender" bool NOT NULL,
  "tel" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "email" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "status" bool NOT NULL DEFAULT true,
  "enable" bool NOT NULL DEFAULT true,
  "nickname" varchar(200) COLLATE "pg_catalog"."default" NOT NULL
)
;

-- ----------------------------
-- Table structure for script
-- ----------------------------
DROP TABLE IF EXISTS "public"."script";
CREATE TABLE "public"."script" (
  "id" int4 NOT NULL DEFAULT nextval('script_id'::regclass),
  "content" text COLLATE "pg_catalog"."default" NOT NULL,
  "environment" text COLLATE "pg_catalog"."default" NOT NULL,
  "enable" bool NOT NULL
)
;
COMMENT ON TABLE "public"."script" IS '话术表';

-- ----------------------------
-- Table structure for task
-- ----------------------------
DROP TABLE IF EXISTS "public"."task";
CREATE TABLE "public"."task" (
  "id" int4 NOT NULL DEFAULT nextval('user_id'::regclass),
  "message_id" int4 NOT NULL
)
;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS "public"."user";
CREATE TABLE "public"."user" (
  "id" int4 NOT NULL DEFAULT nextval('user_id_seq1'::regclass),
  "username" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "nickname" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "password" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "role" "public"."user_role" NOT NULL DEFAULT 'salesman'::user_role,
  "organization" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "tel" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
  "phone" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
  "email" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "qq" varchar(255) COLLATE "pg_catalog"."default" NULL,
  "wechat" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "active" bool NOT NULL DEFAULT true,
  "disable" bool NOT NULL DEFAULT false
)
;

-- ----------------------------
-- Function structure for sys_stat_statements
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."sys_stat_statements"("showtext" bool, OUT "userid" oid, OUT "dbid" oid, OUT "queryid" int8, OUT "query" text, OUT "parses" int8, OUT "total_parse_time" float8, OUT "min_parse_time" float8, OUT "max_parse_time" float8, OUT "mean_parse_time" float8, OUT "stddev_parse_time" float8, OUT "plans" int8, OUT "total_plan_time" float8, OUT "min_plan_time" float8, OUT "max_plan_time" float8, OUT "mean_plan_time" float8, OUT "stddev_plan_time" float8, OUT "calls" int8, OUT "total_exec_time" float8, OUT "min_exec_time" float8, OUT "max_exec_time" float8, OUT "mean_exec_time" float8, OUT "stddev_exec_time" float8, OUT "rows" int8, OUT "shared_blks_hit" int8, OUT "shared_blks_read" int8, OUT "shared_blks_dirtied" int8, OUT "shared_blks_written" int8, OUT "local_blks_hit" int8, OUT "local_blks_read" int8, OUT "local_blks_dirtied" int8, OUT "local_blks_written" int8, OUT "temp_blks_read" int8, OUT "temp_blks_written" int8, OUT "blk_read_time" float8, OUT "blk_write_time" float8);
CREATE FUNCTION "public"."sys_stat_statements"(IN "showtext" bool, OUT "userid" oid, OUT "dbid" oid, OUT "queryid" int8, OUT "query" text, OUT "parses" int8, OUT "total_parse_time" float8, OUT "min_parse_time" float8, OUT "max_parse_time" float8, OUT "mean_parse_time" float8, OUT "stddev_parse_time" float8, OUT "plans" int8, OUT "total_plan_time" float8, OUT "min_plan_time" float8, OUT "max_plan_time" float8, OUT "mean_plan_time" float8, OUT "stddev_plan_time" float8, OUT "calls" int8, OUT "total_exec_time" float8, OUT "min_exec_time" float8, OUT "max_exec_time" float8, OUT "mean_exec_time" float8, OUT "stddev_exec_time" float8, OUT "rows" int8, OUT "shared_blks_hit" int8, OUT "shared_blks_read" int8, OUT "shared_blks_dirtied" int8, OUT "shared_blks_written" int8, OUT "local_blks_hit" int8, OUT "local_blks_read" int8, OUT "local_blks_dirtied" int8, OUT "local_blks_written" int8, OUT "temp_blks_read" int8, OUT "temp_blks_written" int8, OUT "blk_read_time" float8, OUT "blk_write_time" float8)
  RETURNS SETOF "pg_catalog"."record" AS '$libdir/sys_stat_statements', 'sys_stat_statements_1_8'
  LANGUAGE c VOLATILE STRICT
  COST 1
  ROWS 1000;

-- ----------------------------
-- Function structure for sys_stat_statements_all
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."sys_stat_statements_all"("showtext" bool, OUT "userid" oid, OUT "dbid" oid, OUT "queryid" int8, OUT "parent_queryid" int8, OUT "query" text, OUT "parses" int8, OUT "total_parse_time" float8, OUT "min_parse_time" float8, OUT "max_parse_time" float8, OUT "mean_parse_time" float8, OUT "stddev_parse_time" float8, OUT "plans" int8, OUT "total_plan_time" float8, OUT "min_plan_time" float8, OUT "max_plan_time" float8, OUT "mean_plan_time" float8, OUT "stddev_plan_time" float8, OUT "calls" int8, OUT "total_exec_time" float8, OUT "min_exec_time" float8, OUT "max_exec_time" float8, OUT "mean_exec_time" float8, OUT "stddev_exec_time" float8, OUT "rows" int8, OUT "shared_blks_hit" int8, OUT "shared_blks_read" int8, OUT "shared_blks_dirtied" int8, OUT "shared_blks_written" int8, OUT "local_blks_hit" int8, OUT "local_blks_read" int8, OUT "local_blks_dirtied" int8, OUT "local_blks_written" int8, OUT "temp_blks_read" int8, OUT "temp_blks_written" int8, OUT "blk_read_time" float8, OUT "blk_write_time" float8);
CREATE FUNCTION "public"."sys_stat_statements_all"(IN "showtext" bool, OUT "userid" oid, OUT "dbid" oid, OUT "queryid" int8, OUT "parent_queryid" int8, OUT "query" text, OUT "parses" int8, OUT "total_parse_time" float8, OUT "min_parse_time" float8, OUT "max_parse_time" float8, OUT "mean_parse_time" float8, OUT "stddev_parse_time" float8, OUT "plans" int8, OUT "total_plan_time" float8, OUT "min_plan_time" float8, OUT "max_plan_time" float8, OUT "mean_plan_time" float8, OUT "stddev_plan_time" float8, OUT "calls" int8, OUT "total_exec_time" float8, OUT "min_exec_time" float8, OUT "max_exec_time" float8, OUT "mean_exec_time" float8, OUT "stddev_exec_time" float8, OUT "rows" int8, OUT "shared_blks_hit" int8, OUT "shared_blks_read" int8, OUT "shared_blks_dirtied" int8, OUT "shared_blks_written" int8, OUT "local_blks_hit" int8, OUT "local_blks_read" int8, OUT "local_blks_dirtied" int8, OUT "local_blks_written" int8, OUT "temp_blks_read" int8, OUT "temp_blks_written" int8, OUT "blk_read_time" float8, OUT "blk_write_time" float8)
  RETURNS SETOF "pg_catalog"."record" AS '$libdir/sys_stat_statements', 'sys_stat_statements_1_10'
  LANGUAGE c VOLATILE STRICT
  COST 1
  ROWS 1000;

-- ----------------------------
-- Function structure for sys_stat_statements_get_reset_time
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."sys_stat_statements_get_reset_time"(OUT "reset_time" timestamptz);
CREATE FUNCTION "public"."sys_stat_statements_get_reset_time"(OUT "reset_time" timestamptz)
  RETURNS "pg_catalog"."timestamptz" AS '$libdir/sys_stat_statements', 'sys_stat_statements_get_reset_time'
  LANGUAGE c VOLATILE STRICT
  COST 1;

-- ----------------------------
-- Function structure for sys_stat_statements_limit_len
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."sys_stat_statements_limit_len"("showtext" bool, "limit_query_len" int4, OUT "userid" oid, OUT "dbid" oid, OUT "queryid" int8, OUT "query" text, OUT "parses" int8, OUT "total_parse_time" float8, OUT "min_parse_time" float8, OUT "max_parse_time" float8, OUT "mean_parse_time" float8, OUT "stddev_parse_time" float8, OUT "plans" int8, OUT "total_plan_time" float8, OUT "min_plan_time" float8, OUT "max_plan_time" float8, OUT "mean_plan_time" float8, OUT "stddev_plan_time" float8, OUT "calls" int8, OUT "total_exec_time" float8, OUT "min_exec_time" float8, OUT "max_exec_time" float8, OUT "mean_exec_time" float8, OUT "stddev_exec_time" float8, OUT "rows" int8, OUT "shared_blks_hit" int8, OUT "shared_blks_read" int8, OUT "shared_blks_dirtied" int8, OUT "shared_blks_written" int8, OUT "local_blks_hit" int8, OUT "local_blks_read" int8, OUT "local_blks_dirtied" int8, OUT "local_blks_written" int8, OUT "temp_blks_read" int8, OUT "temp_blks_written" int8, OUT "blk_read_time" float8, OUT "blk_write_time" float8);
CREATE FUNCTION "public"."sys_stat_statements_limit_len"(IN "showtext" bool, IN "limit_query_len" int4, OUT "userid" oid, OUT "dbid" oid, OUT "queryid" int8, OUT "query" text, OUT "parses" int8, OUT "total_parse_time" float8, OUT "min_parse_time" float8, OUT "max_parse_time" float8, OUT "mean_parse_time" float8, OUT "stddev_parse_time" float8, OUT "plans" int8, OUT "total_plan_time" float8, OUT "min_plan_time" float8, OUT "max_plan_time" float8, OUT "mean_plan_time" float8, OUT "stddev_plan_time" float8, OUT "calls" int8, OUT "total_exec_time" float8, OUT "min_exec_time" float8, OUT "max_exec_time" float8, OUT "mean_exec_time" float8, OUT "stddev_exec_time" float8, OUT "rows" int8, OUT "shared_blks_hit" int8, OUT "shared_blks_read" int8, OUT "shared_blks_dirtied" int8, OUT "shared_blks_written" int8, OUT "local_blks_hit" int8, OUT "local_blks_read" int8, OUT "local_blks_dirtied" int8, OUT "local_blks_written" int8, OUT "temp_blks_read" int8, OUT "temp_blks_written" int8, OUT "blk_read_time" float8, OUT "blk_write_time" float8)
  RETURNS SETOF "pg_catalog"."record" AS '$libdir/sys_stat_statements', 'sys_stat_statements_1_9'
  LANGUAGE c VOLATILE STRICT
  COST 1
  ROWS 1000;

-- ----------------------------
-- Function structure for sys_stat_statements_reset
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."sys_stat_statements_reset"("userid" oid, "dbid" oid, "queryid" int8);
CREATE FUNCTION "public"."sys_stat_statements_reset"("userid" oid=0, "dbid" oid=0, "queryid" int8=0)
  RETURNS "pg_catalog"."void" AS '$libdir/sys_stat_statements', 'sys_stat_statements_reset_1_7'
  LANGUAGE c VOLATILE STRICT
  COST 1;

-- ----------------------------
-- View structure for sys_stat_statements_all
-- ----------------------------
DROP VIEW IF EXISTS "public"."sys_stat_statements_all";
CREATE VIEW "public"."sys_stat_statements_all" AS  SELECT sys_stat_statements_all.userid,
    sys_stat_statements_all.dbid,
    sys_stat_statements_all.queryid,
    sys_stat_statements_all.parent_queryid,
    sys_stat_statements_all.query,
    sys_stat_statements_all.parses,
    sys_stat_statements_all.total_parse_time,
    sys_stat_statements_all.min_parse_time,
    sys_stat_statements_all.max_parse_time,
    sys_stat_statements_all.mean_parse_time,
    sys_stat_statements_all.stddev_parse_time,
    sys_stat_statements_all.plans,
    sys_stat_statements_all.total_plan_time,
    sys_stat_statements_all.min_plan_time,
    sys_stat_statements_all.max_plan_time,
    sys_stat_statements_all.mean_plan_time,
    sys_stat_statements_all.stddev_plan_time,
    sys_stat_statements_all.calls,
    sys_stat_statements_all.total_exec_time,
    sys_stat_statements_all.min_exec_time,
    sys_stat_statements_all.max_exec_time,
    sys_stat_statements_all.mean_exec_time,
    sys_stat_statements_all.stddev_exec_time,
    sys_stat_statements_all.rows,
    sys_stat_statements_all.shared_blks_hit,
    sys_stat_statements_all.shared_blks_read,
    sys_stat_statements_all.shared_blks_dirtied,
    sys_stat_statements_all.shared_blks_written,
    sys_stat_statements_all.local_blks_hit,
    sys_stat_statements_all.local_blks_read,
    sys_stat_statements_all.local_blks_dirtied,
    sys_stat_statements_all.local_blks_written,
    sys_stat_statements_all.temp_blks_read,
    sys_stat_statements_all.temp_blks_written,
    sys_stat_statements_all.blk_read_time,
    sys_stat_statements_all.blk_write_time
   FROM sys_stat_statements_all(true) sys_stat_statements_all(userid, dbid, queryid, parent_queryid, query, parses, total_parse_time, min_parse_time, max_parse_time, mean_parse_time, stddev_parse_time, plans, total_plan_time, min_plan_time, max_plan_time, mean_plan_time, stddev_plan_time, calls, total_exec_time, min_exec_time, max_exec_time, mean_exec_time, stddev_exec_time, rows, shared_blks_hit, shared_blks_read, shared_blks_dirtied, shared_blks_written, local_blks_hit, local_blks_read, local_blks_dirtied, local_blks_written, temp_blks_read, temp_blks_written, blk_read_time, blk_write_time);

-- ----------------------------
-- View structure for sys_stat_statements
-- ----------------------------
DROP VIEW IF EXISTS "public"."sys_stat_statements";
CREATE VIEW "public"."sys_stat_statements" AS  SELECT sys_stat_statements_all.userid,
    sys_stat_statements_all.dbid,
    sys_stat_statements_all.queryid,
    sys_stat_statements_all.query,
    sys_stat_statements_all.parses,
    sys_stat_statements_all.total_parse_time,
    sys_stat_statements_all.min_parse_time,
    sys_stat_statements_all.max_parse_time,
    sys_stat_statements_all.mean_parse_time,
    sys_stat_statements_all.stddev_parse_time,
    sys_stat_statements_all.plans,
    sys_stat_statements_all.total_plan_time,
    sys_stat_statements_all.min_plan_time,
    sys_stat_statements_all.max_plan_time,
    sys_stat_statements_all.mean_plan_time,
    sys_stat_statements_all.stddev_plan_time,
    sys_stat_statements_all.calls,
    sys_stat_statements_all.total_exec_time,
    sys_stat_statements_all.min_exec_time,
    sys_stat_statements_all.max_exec_time,
    sys_stat_statements_all.mean_exec_time,
    sys_stat_statements_all.stddev_exec_time,
    sys_stat_statements_all.rows,
    sys_stat_statements_all.shared_blks_hit,
    sys_stat_statements_all.shared_blks_read,
    sys_stat_statements_all.shared_blks_dirtied,
    sys_stat_statements_all.shared_blks_written,
    sys_stat_statements_all.local_blks_hit,
    sys_stat_statements_all.local_blks_read,
    sys_stat_statements_all.local_blks_dirtied,
    sys_stat_statements_all.local_blks_written,
    sys_stat_statements_all.temp_blks_read,
    sys_stat_statements_all.temp_blks_written,
    sys_stat_statements_all.blk_read_time,
    sys_stat_statements_all.blk_write_time
   FROM sys_stat_statements_all
  WHERE sys_stat_statements_all.parent_queryid = 0 OR sys_stat_statements_all.queryid IS NULL;

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."audit_id"', 82, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."audit_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."audit_id_seq1"
OWNED BY "public"."audit"."id";
SELECT setval('"public"."audit_id_seq1"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."consigner_id"', 4, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."consigner_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."consigner_id_seq1"
OWNED BY "public"."consigner"."id";
SELECT setval('"public"."consigner_id_seq1"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."group_consigner_id"', 12, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."group_consigner_id_seq"
OWNED BY "public"."group_consigner"."id";
SELECT setval('"public"."group_consigner_id_seq"', 12, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."group_id"', 10, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."group_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."group_id_seq1"
OWNED BY "public"."group"."id";
SELECT setval('"public"."group_id_seq1"', 12, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."group_member_id"', 25, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."group_member_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."group_member_id_seq1"
OWNED BY "public"."group_member"."id";
SELECT setval('"public"."group_member_id_seq1"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."group_production_id"', 41, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."group_production_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."group_production_id_seq1"
OWNED BY "public"."group_production"."id";
SELECT setval('"public"."group_production_id_seq1"', 15, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."group_robot_id"', 62, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."group_robot_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."group_robot_id_seq1"
OWNED BY "public"."group_robot"."id";
SELECT setval('"public"."group_robot_id_seq1"', 18, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."group_trader_id"', 20, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."group_trader_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."group_trader_id_seq1"
OWNED BY "public"."group_trader"."id";
SELECT setval('"public"."group_trader_id_seq1"', 15, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."id"', 10, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."instruction_id"', 92, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."instruction_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."instruction_id_seq1"
OWNED BY "public"."instruction"."id";
SELECT setval('"public"."instruction_id_seq1"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."log_id"', 8, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."log_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."log_id_seq1"
OWNED BY "public"."log"."id";
SELECT setval('"public"."log_id_seq1"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."message_id"', 192, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."message_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."message_id_seq1"
OWNED BY "public"."message"."id";
SELECT setval('"public"."message_id_seq1"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."production_id"', 3, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."production_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."production_id_seq1"
OWNED BY "public"."production"."id";
SELECT setval('"public"."production_id_seq1"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."robot_id"', 109, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."robot_id_seq"', 10, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."robot_id_seq1"
OWNED BY "public"."robot"."id";
SELECT setval('"public"."robot_id_seq1"', 9, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."script_id"', 5, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."script_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."script_id_seq1"
OWNED BY "public"."script"."id";
SELECT setval('"public"."script_id_seq1"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."task_id"', 41, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."task_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."task_id_seq1"
OWNED BY "public"."task"."id";
SELECT setval('"public"."task_id_seq1"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."user_id"', 90, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."user_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."user_id_seq1"
OWNED BY "public"."user"."id";
SELECT setval('"public"."user_id_seq1"', 2, true);

-- ----------------------------
-- Indexes structure for table audit
-- ----------------------------
CREATE INDEX "idx_audit_status" ON "public"."audit" USING btree (
  "status" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table audit
-- ----------------------------
ALTER TABLE "public"."audit" ADD CONSTRAINT "audit_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table consigner
-- ----------------------------
ALTER TABLE "public"."consigner" ADD CONSTRAINT "consigner_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table group
-- ----------------------------
CREATE INDEX "idx_group_number" ON "public"."group" USING btree (
  "number" COLLATE "pg_catalog"."default" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table group
-- ----------------------------
ALTER TABLE "public"."group" ADD CONSTRAINT "group_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table group_consigner
-- ----------------------------
ALTER TABLE "public"."group_consigner" ADD CONSTRAINT "group_consigner_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table group_member
-- ----------------------------
ALTER TABLE "public"."group_member" ADD CONSTRAINT "group_member_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table group_production
-- ----------------------------
ALTER TABLE "public"."group_production" ADD CONSTRAINT "group_production_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table group_robot
-- ----------------------------
ALTER TABLE "public"."group_robot" ADD CONSTRAINT "group_robot_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table group_trader
-- ----------------------------
ALTER TABLE "public"."group_trader" ADD CONSTRAINT "group_trader_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table instruction
-- ----------------------------
ALTER TABLE "public"."instruction" ADD CONSTRAINT "instruction_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table log
-- ----------------------------
ALTER TABLE "public"."log" ADD CONSTRAINT "log_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table message
-- ----------------------------
CREATE INDEX "idx_message_date" ON "public"."message" USING btree (
  "date" ASC NULLS LAST
);
CREATE INDEX "idx_message_receiver" ON "public"."message" USING btree (
  "receiver" COLLATE "pg_catalog"."default" ASC NULLS LAST
);
CREATE INDEX "idx_message_sender" ON "public"."message" USING btree (
  "sender" COLLATE "pg_catalog"."default" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table message
-- ----------------------------
ALTER TABLE "public"."message" ADD CONSTRAINT "message_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table production
-- ----------------------------
ALTER TABLE "public"."production" ADD CONSTRAINT "production_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table robot
-- ----------------------------
CREATE INDEX "idx_robot_account" ON "public"."robot" USING btree (
  "account" COLLATE "pg_catalog"."default" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table robot
-- ----------------------------
ALTER TABLE "public"."robot" ADD CONSTRAINT "robot_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table script
-- ----------------------------
ALTER TABLE "public"."script" ADD CONSTRAINT "script_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table task
-- ----------------------------
ALTER TABLE "public"."task" ADD CONSTRAINT "task_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table user
-- ----------------------------
CREATE INDEX "idx_user_username" ON "public"."user" USING btree (
  "username" COLLATE "pg_catalog"."default" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table user
-- ----------------------------
ALTER TABLE "public"."user" ADD CONSTRAINT "user_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table audit
-- ----------------------------
ALTER TABLE "public"."audit" ADD CONSTRAINT "fk_audit_auditor_id" FOREIGN KEY ("auditor_id") REFERENCES "public"."user" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."audit" ADD CONSTRAINT "fk_audit_instruction_id" FOREIGN KEY ("instruction_id") REFERENCES "public"."instruction" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."audit" ADD CONSTRAINT "fk_audit_message" FOREIGN KEY ("message") REFERENCES "public"."message" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table group_consigner
-- ----------------------------
ALTER TABLE "public"."group_consigner" ADD CONSTRAINT "fk_group_consigner_consigner_id" FOREIGN KEY ("consigner_id") REFERENCES "public"."consigner" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."group_consigner" ADD CONSTRAINT "fk_group_consigner_group_id" FOREIGN KEY ("group_id") REFERENCES "public"."group" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table group_production
-- ----------------------------
ALTER TABLE "public"."group_production" ADD CONSTRAINT "fk_group_production_group_id" FOREIGN KEY ("group_id") REFERENCES "public"."group" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."group_production" ADD CONSTRAINT "fk_group_production_production_id" FOREIGN KEY ("production_id") REFERENCES "public"."production" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table group_robot
-- ----------------------------
ALTER TABLE "public"."group_robot" ADD CONSTRAINT "fk_group_robot_group_id" FOREIGN KEY ("group_id") REFERENCES "public"."group" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."group_robot" ADD CONSTRAINT "fk_group_robot_robot_id" FOREIGN KEY ("robot_id") REFERENCES "public"."robot" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table group_trader
-- ----------------------------
ALTER TABLE "public"."group_trader" ADD CONSTRAINT "fk_group_trader_group_id" FOREIGN KEY ("group_id") REFERENCES "public"."group" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table instruction
-- ----------------------------
ALTER TABLE "public"."instruction" ADD CONSTRAINT "fk_instruction_task_id" FOREIGN KEY ("task_id") REFERENCES "public"."task" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table task
-- ----------------------------
ALTER TABLE "public"."task" ADD CONSTRAINT "fk_task_message_id" FOREIGN KEY ("message_id") REFERENCES "public"."message" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
