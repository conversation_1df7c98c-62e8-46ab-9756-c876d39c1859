"""init:init

Revision ID: 25fd545291b6
Revises: 
Create Date: 2025-06-24 11:20:18.197764

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '25fd545291b6'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ewx_group',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('robot', sa.Integer(), nullable=False),
    sa.Column('groupAnnouncement', sa.Text(), nullable=True),
    sa.Column('groupName', sa.String(length=255), nullable=False),
    sa.Column('createTime', sa.Date(), nullable=False),
    sa.Column('level', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ewx_group_id'), 'ewx_group', ['id'], unique=False)
    op.create_table('ewx_message',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('date', sa.DateTime(), nullable=False),
    sa.Column('atme', sa.Boolean(), nullable=False),
    sa.Column('type', sa.Integer(), nullable=False),
    sa.Column('sender', sa.String(length=100), nullable=False),
    sa.Column('receiver', sa.String(length=100), nullable=False),
    sa.Column('position', sa.String(length=255), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('ewx_robot',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('robotId', sa.String(length=255), nullable=False),
    sa.Column('name', sa.String(length=150), nullable=False),
    sa.Column('corporation', sa.String(length=150), nullable=False),
    sa.Column('sumInfo', sa.String(length=255), nullable=False),
    sa.Column('openCallback', sa.Boolean(), nullable=False),
    sa.Column('encryptType', sa.Boolean(), nullable=False),
    sa.Column('callbackUrl', sa.String(length=255), nullable=False),
    sa.Column('enableAdd', sa.Boolean(), nullable=False),
    sa.Column('replyAll', sa.Boolean(), nullable=False),
    sa.Column('robotKeyCheck', sa.Boolean(), nullable=False),
    sa.Column('callBackRequestType', sa.Integer(), nullable=False),
    sa.Column('robotType', sa.Boolean(), nullable=False),
    sa.Column('firstLogin', sa.Date(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('authExpir', sa.Date(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ewx_robot_id'), 'ewx_robot', ['id'], unique=False)
    op.create_table('qq_group',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('group_id', sa.String(length=100), nullable=False),
    sa.Column('group_name', sa.String(length=255), nullable=False),
    sa.Column('member', sa.Integer(), nullable=False),
    sa.Column('max_member', sa.Integer(), nullable=False),
    sa.Column('robot', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_qq_group_id'), 'qq_group', ['id'], unique=False)
    op.create_table('qq_robot',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('account', sa.String(length=100), nullable=False),
    sa.Column('nickname', sa.String(length=255), nullable=False),
    sa.Column('personal_note', sa.Text(), nullable=True),
    sa.Column('sex', sa.Boolean(), nullable=False),
    sa.Column('address', sa.String(length=255), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('bearer', sa.String(length=100), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_qq_robot_id'), 'qq_robot', ['id'], unique=False)
    op.drop_table('task')
    op.alter_column('user', 'email',
               existing_type=mysql.VARCHAR(length=150),
               type_=sa.String(length=100),
               existing_nullable=False)
    op.create_index(op.f('ix_user_id'), 'user', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_id'), table_name='user')
    op.alter_column('user', 'email',
               existing_type=sa.String(length=100),
               type_=mysql.VARCHAR(length=150),
               existing_nullable=False)
    op.create_table('task',
    sa.Column('id', mysql.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('type', mysql.INTEGER(), autoincrement=False, nullable=False, comment='捞单解析/指令更改/指令撤回'),
    sa.Column('date', mysql.DATETIME(), nullable=False),
    sa.Column('category', mysql.VARCHAR(length=50), nullable=False, comment='业务分类'),
    sa.Column('execution', sa.DATE(), nullable=False, comment='执行日'),
    sa.Column('product', mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_0900_ai_ci', length=150), nullable=False, comment='产品名'),
    sa.Column('standard', mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_0900_ai_ci', length=150), nullable=False, comment='标准产品代码/名称'),
    sa.Column('direction', mysql.TINYINT(display_width=1), autoincrement=False, nullable=False, comment='买入/买出（方向）'),
    sa.Column('code', mysql.VARCHAR(length=150), nullable=False, comment='证券代码'),
    sa.Column('remark', mysql.VARCHAR(length=255), nullable=False, comment='备注（指令已更改，已撤回...）'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.drop_index(op.f('ix_qq_robot_id'), table_name='qq_robot')
    op.drop_table('qq_robot')
    op.drop_index(op.f('ix_qq_group_id'), table_name='qq_group')
    op.drop_table('qq_group')
    op.drop_index(op.f('ix_ewx_robot_id'), table_name='ewx_robot')
    op.drop_table('ewx_robot')
    op.drop_table('ewx_message')
    op.drop_index(op.f('ix_ewx_group_id'), table_name='ewx_group')
    op.drop_table('ewx_group')
    # ### end Alembic commands ###