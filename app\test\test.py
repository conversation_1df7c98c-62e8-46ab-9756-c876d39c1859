import datetime

import requests
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.models import Audit

#OK

# if __name__ == '__main__':
#     payload = {
#         "guid_no": 2,
#         "operator_code": "baidutest1",
#         "external_order_no": 2,
#         "fund_code": 611922,
#         "report_code": "068047",
#         "entrust_direction": "3",
#         "face_balance": 10000,
#         "clear_speed": "0",
#         "direct_operator_code": "baidutest1",
#         "ins_begin_date":datetime.datetime.now().strftime("%Y%m%d"),
#         "yield_rate": 3.2,
#         "warrant_yield_rate": 1
#     }
#     response = requests.post( "http://10.7.111.36:12707/ib/bond/insdir", json=payload)
#     response.raise_for_status()
#     print(response.json())

#OK

# if __name__ == '__main__':
#     payload = {
#         "guid_no":2,
#         "operator_code": "baidutest1",
#         "external_order_no": 2,
#         "fund_code":611922,
#         "market_no": 1,
#         "report_code": "115004",
#         "entrust_direction": "3",
#         "ins_price":100000,
#         "direct_operator_code": "baidutest1",
#         "ins_begin_date": "20250710",
#         "ins_end_date": "20250710",
#         "invest_type":1,
#         "ins_amount":1000
#     }
#     response = requests.post( "http://10.7.111.36:12707/ufx/shfix/exsecuritis/insdir", json=payload)
#     response.raise_for_status()
#     print(response.json())
#


#草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草草

import requests

if __name__ == '__main__':
    payload = {
        "guid_no": 22,
        "operator_code": "baidutest1",
        "external_order_no": 22,
        "fund_code": 611922,
        "entrust_direction": "5",
        "hg_days": 1,
        "ins_balance": 10000,
        "ins_price": 1000,
        "clear_speed": "0",
        "direct_operator_code": "baidutest1",
        "ins_begin_date": 20250715,
        "mortgageinfo_in": [
            {
                "report_code": "2480064",
                "invest_type": "1",
                "face_balance": 100000,
                "mortgage_ratio": 0.1,
                "mortgage_type": "1",
            },
        ]
    }

    try:
        response = requests.post("http://10.7.111.36:12707/ib/repo/insdir", json=payload)
        response.raise_for_status()

        print("状态码:", response.status_code)
        print("响应头:")
        for key, value in response.headers.items():
            print(f"  {key}: {value}")
        print("响应体（JSON）:")
        print(response.json())

    except requests.exceptions.HTTPError as errh:
        print("HTTP错误:", errh)
    except requests.exceptions.ConnectionError as errc:
        print("连接错误:", errc)
    except requests.exceptions.Timeout as errt:
        print("超时错误:", errt)
    except requests.exceptions.RequestException as err:
        print("请求异常:", err)


############
#
# if __name__ == '__main__':
#     payload = {
#         "guid_no": 14,
#         "operator_code": "baidutest1",
#         "external_order_no": 14,
#         "fund_code": 611922,
#         "market_no": 2,
#         "report_code": "baidutest1",
#         "entrust_direction": 103,
#         "hg_days": 1,
#         "ins_balance": 100000,
#         "ins_price": 100000,
#         "direct_operator_code": "baidutest1",
#         "ins_begin_date": 20250714,
#         "ins_end_date": 20250714,
#         # "ins_begin_date":datetime.strptime(audit.execution, "%Y-%m-%d %H:%M:%S.%f").strftime("%Y%m%d") if audit.execution else None,,
#         "mortgage_type": 1,
#         "mortgage_ratio": 1,
#         "mortgage_amount": 1,
#     }
#     response = requests.post( "http://10.7.111.36:12707/ufx/negotiated/epurchase", json=payload)
#     response.raise_for_status()
#     print(response.json())


