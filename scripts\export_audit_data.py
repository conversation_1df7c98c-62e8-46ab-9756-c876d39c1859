#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
审核数据导出脚本
根据 Message 表关联 Audit 数据并导出为 Excel 文件
"""

import argparse
import logging
import pandas as pd
from datetime import datetime
from sqlalchemy.orm import Session
from typing import List, Dict, Any
import json

from app.core.database import get_db
from app.models.models import Audit, Instruction, Task, Message

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 字段映射（从 audit.py 复制）
AUDIT_VARIABLE_MAPPING = {
    "id": "ID",
    "variety": "品种",
    "market": "市场", 
    "classification": "分类",
    "execution": "执行日",
    "production": "产品名称",
    "production_fund_caption": "标准产品名称",
    "production_id": "标准产品代码",
    "direction": "方向",
    "security_code": "证券代码",
    "security_name": "证券名",
    "yield_rate": "收益率",
    "net_price": "净价/委托价格",
    "full_price": "全价",
    "denomination": "金额/面额",
    "rival": "对手方",
    "rival_trader": "对手方交易员",
    "status": "状态",
    "message": "原始消息",
    "created_at": "创建时间",
    "updated_at": "更新时间"
}

def decode_unicode_json(json_str: str) -> dict:
    """
    解码包含 Unicode 编码的 JSON 字符串
    支持多种编码格式混合的情况
    
    Args:
        json_str: 包含编码的 JSON 字符串
    
    Returns:
        dict: 解码后的字典
    """
    try:
        # 解析 JSON
        data = json.loads(json_str)
        
        # 递归解码
        def decode_recursive(obj):
            if isinstance(obj, dict):
                return {key: decode_recursive(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [decode_recursive(item) for item in obj]
            elif isinstance(obj, str):
                # 尝试多种解码方式
                try:
                    # 1. 尝试 Unicode 转义序列解码
                    if '\\u' in obj:
                        return obj.encode().decode('unicode_escape')
                    
                    # 2. 尝试 UTF-8 乱码修复
                    # 检测是否为 UTF-8 被错误解码为 latin-1 的情况
                    if any(ord(c) > 127 for c in obj):
                        try:
                            # 先编码为 latin-1，再解码为 UTF-8
                            return obj.encode('latin-1').decode('utf-8')
                        except:
                            pass
                    
                    return obj
                except:
                    return obj
            else:
                return obj
        
        return decode_recursive(data)
    except Exception as e:
        logging.warning(f"JSON 解码失败: {e}")
        return None

def format_o45_result(result_str: str) -> str:
    """
    格式化 O45 返回结果
    处理各种编码问题
    
    Args:
        result_str: O45 返回的原始字符串
    
    Returns:
        str: 格式化后的字符串
    """
    if not result_str:
        return ""
    
    # 尝试解码
    decoded_data = decode_unicode_json(result_str)
    if decoded_data:
        return json.dumps(decoded_data, ensure_ascii=False, indent=2)
    
    # 如果解码失败，尝试直接修复 UTF-8 乱码
    try:
        if any(ord(c) > 127 for c in result_str):
            fixed_str = result_str.encode('latin-1').decode('utf-8')
            return fixed_str
    except:
        pass
    
    return result_str

message_type_dict = {
    '0': '文本消息',
    '1': '引用文本消息',
    '2': '图片消息',
    '3': '引用图片消息',
    0: '文本消息',
    1: '引用文本消息',
    2: '图片消息',
    3: '引用图片消息',
}
def export_audit_data(
    output_file: str = None,
    status_filter: List[int] = None,
    date_from: str = None,
    date_to: str = None
) -> str:
    """
    导出审核数据到 Excel
    
    Args:
        output_file: 输出文件路径
        status_filter: 状态过滤列表
        date_from: 开始日期 (YYYY-MM-DD)
        date_to: 结束日期 (YYYY-MM-DD)
    
    Returns:
        str: 导出文件路径
    """
    db_gen = get_db()
    db: Session = next(db_gen)
    
    try:
        # 构建查询
        query = (
            db.query(Audit, Instruction, Task, Message)
            .join(Instruction, Audit.instruction_id == Instruction.id)
            .join(Task, Instruction.task_id == Task.id)
            .join(Message, Task.message_id == Message.id)
        )
        
        # 添加状态过滤
        if status_filter:
            query = query.filter(Audit.status.in_(status_filter))
        
        # 添加日期过滤
        if date_from:
            query = query.filter(Audit.created_at >= datetime.strptime(date_from, '%Y-%m-%d'))
        if date_to:
            query = query.filter(Audit.created_at <= datetime.strptime(date_to + ' 23:59:59', '%Y-%m-%d %H:%M:%S'))
        
        # 执行查询
        results = query.all()
        logging.info(f"查询到 {len(results)} 条记录")
        
        # 准备数据
        data_list = []
        for audit, instruction, task, message in results:
            row_data = {
                # Message 字段
                '消息ID': message.id,
                '消息内容': message.content,
                '消息日期': message.date,
                '消息类型': message_type_dict[str(message.type)] if message.type in message_type_dict else '未知类型',
                '意图识别返回值': format_o45_result(message.result) if hasattr(message,
                                                                               'result') and message.result else "",

                # Instruction 字段
                '指令ID': instruction.id,

                # Task 字段
                '任务ID': task.id,

                # Audit 字段
                'ID': audit.id,
                '品种': audit.variety,
                '市场': audit.market,
                '分类': audit.classification,
                '执行日': audit.execution,
                '产品名称': audit.production,
                '标准产品名称': audit.production_fund_caption,
                '标准产品代码': audit.production_id,
                '方向': audit.direction,
                '证券代码': audit.security_code,
                '证券名': audit.security_name,
                '收益率': audit.yield_rate,
                '净价/委托价格': audit.net_price,
                '全价': audit.full_price,
                '金额/面额': audit.denomination,
                '对手方': audit.rival,
                '对手方交易员': audit.rival_trader,
                '状态': audit.status,
                'O45返回值': audit.o45_return_json,
                

            }
            data_list.append(row_data)
        
        # 创建 DataFrame
        df = pd.DataFrame(data_list)
        
        # 生成输出文件名
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f'audit_export_{timestamp}.xlsx'
        
        # 导出到 Excel
        df.to_excel(output_file, index=False, engine='openpyxl')
        logging.info(f"数据已导出到: {output_file}")
        
        return output_file
        
    except Exception as e:
        logging.error(f"导出失败: {str(e)}")
        raise
    finally:
        db.close()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="审核数据导出工具")
    parser.add_argument('--output', '-o', help='输出文件路径')
    parser.add_argument('--status', nargs='+', type=int, help='状态过滤 (例: --status 0 1 2)')
    parser.add_argument('--date-from', help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--date-to', help='结束日期 (YYYY-MM-DD)')
    
    args = parser.parse_args()
    
    try:
        output_file = export_audit_data(
            output_file=args.output,
            status_filter=args.status,
            date_from=args.date_from,
            date_to=args.date_to
        )
        print(f"导出完成: {output_file}")
        
    except Exception as e:
        logging.error(f"导出失败: {str(e)}")
        exit(1)

if __name__ == "__main__":
    main()


