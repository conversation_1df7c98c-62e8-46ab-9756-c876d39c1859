from datetime import datetime
import re

def parse_execution_date(date_str: str) -> datetime:
    """
    解析多种格式的日期字符串，返回标准 datetime 对象
    - 格式1: "YYYYMMDD"（如 "20230710"）
    - 格式2: "MMDD"（如 "0710" → 补充当前年份）
    - 格式3: "X月X日"（如 "7月10日" → 补充当前年份）
    - 格式4: YYYY/MM/DD（如 "2023/07/10"）
    - 格式5: MM/DD （如 "07/10" → 补充当前年份）
    - 其他情况: 返回当天日期
    """
    if not date_str:
        return datetime.today()

    # 格式1: YYYYMMDD（8位纯数字）
    if re.match(r"^\d{8}$", date_str):
        try:
            return datetime.strptime(date_str, "%Y%m%d")
        except ValueError:
            pass

    # 格式2: MMDD（4位纯数字）
    if re.match(r"^\d{4}$", date_str):
        try:
            return datetime.strptime(f"{datetime.now().year}{date_str}", "%Y%m%d")
        except ValueError:
            pass

    # 格式3: "X月X日"（中文格式）
    if "月" in date_str and "日" in date_str:
        try:
            month = int(re.search(r"(\d+)月", date_str).group(1))
            day = int(re.search(r"(\d+)日", date_str).group(1))
            return datetime(datetime.now().year, month, day)
        except (AttributeError, ValueError):
            pass

    # 格式4: YYYY/MM/DD
    if re.match(r"^\d{4}/\d{2}/\d{2}$", date_str):
        try:
            return datetime.strptime(date_str, "%Y/%m/%d")
        except ValueError:
            pass

    # 格式5: MM/DD
    if re.match(r"^\d{2}/\d{2}$", date_str):
        try:
            return datetime.strptime(f"{datetime.now().year}/{date_str}", "%Y/%m/%d")
        except ValueError:
            pass

    # 其他情况返回当天
    return datetime.today()

digital_conversion_dict = {
    "W": 10000,
    "W元": 10000,
    "万": 10000,
    "万元": 10000,
    "K": 10000000,
    "KW": 10000000,
    "千": 10000000,
    "KW元": 10000000,
    "千万": 10000000,
    "千万元": 10000000,
    "E": 100000000,
    "亿": 100000000,
    "E元": 100000000,
    "亿元": 100000000,
}


def convert_value(value, product):
    """统一转换数值，支持带单位和纯数字"""
    if isinstance(value, (int, float)):
        return int(value * product)  # 如果是纯数字或浮点数，乘以 10000

    if isinstance(value, str):
        clean_value = value.upper().replace(',', '').strip()
        sorted_units = sorted(digital_conversion_dict.items(),
                              key=lambda x: len(x[0]),
                              reverse=True)

        for unit, multiplier in sorted_units:
            if clean_value.endswith(unit.upper()):
                try:
                    num_part = clean_value[:-len(unit)].strip()
                    # 移除可能的中文括号
                    num_part = re.sub(r'[\(\)（）]', '', num_part)
                    num_value = float(num_part) if '.' in num_part else int(num_part)
                    return int(num_value * multiplier)
                except (ValueError, TypeError):
                    return 0  # 转换失败返回0

        # 没有匹配单位时尝试直接转换
        try:
            num_value = float(clean_value) if '.' in clean_value else int(clean_value)
            return int(num_value * product)  # 如果是纯数字或浮点数，乘以 10000
        except (ValueError, TypeError):
            pass

    return 0  # 所有转换失败返回0


def process_deal_data(deal_data: dict) -> dict:
    processed_data = deal_data.copy()

    # 处理 denomination（面额）
    if 'denomination' in processed_data:
        value = processed_data['denomination']
        processed_data['denomination'] = convert_value(value, 10000)

    # 处理 pledge_quantity（质押数量）
    if 'pledge_quantity' in processed_data:
        value = processed_data['pledge_quantity']

        if isinstance(value, str):
            # 统一分隔符处理
            pledge_items = re.split(r'[、]', value)
            processed_values = []

            for item in pledge_items:
                item = item.strip()
                print(f"处理质押数量项: {item}")
                if item:
                    converted = convert_value(item, 100)
                    if converted > 0:  # 只添加有效转换结果
                        processed_values.append(converted)
                    else:
                        # 如果转换失败，添加原始值
                        processed_values.append(item)

            processed_data['pledge_quantity'] = "、".join(map(str, processed_values))

        elif isinstance(value, (int, float)):
            processed_data['pledge_quantity'] = convert_value(value)

    return processed_data


def process_discount_rates(discount_rates_str):
    """
    处理折扣率字符串，将其转换为统一的百分数格式。

    参数:
        discount_rates_str (str): 包含折扣率的字符串，折扣率之间用逗号分隔。

    返回:
        list: 处理后的折扣率列表，每个折扣率都是百分数格式的字符串。
    """
    if not discount_rates_str:
        return None

    # 这里处理是为了统一格式，方便编辑页面的显示和o45的传值
def process_discount_rates(discount_rates_str):
    """
    处理折扣率字符串，将其转换为统一的百分数格式。

    参数:
        discount_rates_str (str): 包含折扣率的字符串，折扣率之间用逗号分隔。

    返回:
        list: 处理后的折扣率列表，每个折扣率都是百分数格式的字符串。
    """
    if not discount_rates_str:
        return None

    def process_single_rate(rate):
        # 去除可能存在的多余空格
        rate = rate.strip()

        # 如果折扣率已经是以百分数形式（如 "92%"）
        if rate.endswith('%'):
            try:
                # 去掉百分号并转换为浮点数
                rate_value = float(rate[:-1])
                if 0 <= rate_value <= 100:  # 判断是否在合法范围内
                    return f"{rate_value:.2f}"
                else:
                    # 非法值范围置空
                    return ""
            except ValueError:
                # 异常值置空
                return ""
        else:
            # 如果折扣率是一个小数或整数（如 "0.85" 或 "92"）
            try:
                rate_value = float(rate)
                if 0 <= rate_value <= 1:  # 判断是否是小数形式的折扣率 保留两位小数
                    return f"{rate_value * 100:.2f}"
                elif 0 <= rate_value <= 100:  # 判断是否是百分数形式但没有百分号   保留两位小数
                    return f"{rate_value:.2f}"
                else:
                    # 非法值范围置空
                    return ""
            except ValueError:
                # 异常值置空
                return ""

    # 将输入字符串按逗号分隔，切割为列表
    discount_rates = discount_rates_str.split('、')

    # 处理每个折扣率
    processed_rates = [process_single_rate(rate) for rate in discount_rates]

    processed_rates = "、".join(processed_rates)

    return processed_rates


if __name__ == "__main__":

    # 测试 process_deal_data
    deal = {
        "denomination": "300,000",
        "pledge_quantity": "300,000、300,000qresf、300,000、565,000、300,000、163,600"
    }
    processed_deal = process_deal_data(deal)

    text = "0.85、0.85、0.85、0.85、0.85%、85e%"
    print(process_discount_rates(text))
    # print(processed_deal)