#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2019 Tianxiadatong, Co.,Ltd. All Rights Reserved.

This module contains legacy enterprise WeChat robot API endpoint as public constraints.
It is now deprecated and kept for archival or compatibility purposes.

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06
"""


#发送信息URL
send_message_url = "https://api.worktool.ymdyes.cn/wework/sendRawMessage"

#消息回调URL
modify_callback_url = "https://api.worktool.ymdyes.cn/robot/robotInfo/update"