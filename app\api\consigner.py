#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2024 Tianxiadatong, Co., Ltd. All Rights Reserved.

This module implements the FastAPI sub-router for the /consigner endpoint,
including CRUD operations and validation for consigner management.

Features:
- CRUD operations for consigner management
- Phone number and ID card validation
- Role-based access control
- Soft delete support
- Search and pagination

Author: Wait<PERSON>(<EMAIL>)
Date: 2025-07-14 17:23:06
"""

import re
import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field, validator

from app.core.database import get_db
from app.models.models import Consigner, User, UserRole
from app.decorator.auth import required_login
from app.assets.public_assets import general_return_json

router = APIRouter(
    prefix="/consigner",
    tags=["consigner"]
)

class ConsignerBase(BaseModel):
    """委托人基础模型。

    Attributes:
        consigner (str): 委托人姓名
        id_card (Optional[str]): 身份证号，可选，最大长度128字符
        phone (Optional[str]): 手机号，可选，最大长度64字符
    """
    consigner: str
    id_card: Optional[str] = Field(None, max_length=128)
    phone: Optional[str] = Field(None, max_length=64)

    @validator('phone')
    def validate_phone(cls, v):
        """验证手机号格式。

        Args:
            v (Optional[str]): 手机号

        Returns:
            str: 验证通过的手机号，空字符串返回None

        Raises:
            ValueError: 手机号格式不正确时抛出
        """
        if v is None or v.strip() == '':
            return None
        
        pattern = r'^1[3-9]\d{9}$'
        if not re.match(pattern, v):
            raise ValueError('手机号格式不正确，请输入正确的11位手机号')
        return v

    @validator('id_card')
    def validate_id_card(cls, v):
        """验证身份证号格式。

        支持15位和18位身份证号，对18位身份证号进行校验码验证。

        Args:
            v (Optional[str]): 身份证号

        Returns:
            str: 验证通过的身份证号（转换为大写），空字符串返回None

        Raises:
            ValueError: 身份证号格式不正确或校验码错误时抛出
        """
        if v is None or v.strip() == '':
            return None
            
        pattern = r'^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[1-2]\d|3[0-1])\d{3}(?:\d|X|x)$|^[1-9]\d{7}(?:0[1-9]|1[0-2])(?:0[1-9]|[1-2]\d|3[0-1])\d{3}$'
        if not re.match(pattern, v):
            raise ValueError('身份证号格式不正确，请输入正确的15位或18位身份证号')
            
        if len(v) == 18:
            try:
                factors = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
                check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
                
                items = [int(v[i]) * factors[i] for i in range(17)]
                remainder = sum(items) % 11
                check_code = check_codes[remainder]
                
                if check_code.upper() != v[-1].upper():
                    raise ValueError('身份证号校验码不正确')
            except:
                raise ValueError('身份证号格式不正确')
        return v.upper()

class ConsignerCreate(ConsignerBase):
    """用于创建委托人的请求模型。"""
    pass

class ConsignerUpdate(ConsignerBase):
    """用于更新委托人的请求模型。"""
    pass

class ConsignerResponse(ConsignerBase):
    """委托人响应模型。

    Attributes:
        id (int): 委托人ID
        is_deleted (bool): 是否已删除
    """
    id: int
    is_deleted: bool = False
    
    class Config:
        from_attributes = True

@router.get("")
@required_login(roles=["admin", "salesman", "super_admin"])
async def get_consigners(
    request: Request,
    db: Session = Depends(get_db),
    keyword: Optional[str] = Query(None, description="委托人名称关键词（支持模糊查询）"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量")
):
    """获取委托人列表，支持分页和搜索。

    Args:
        request (Request): FastAPI 请求对象
        db (Session): 数据库会话对象
        keyword (Optional[str]): 委托人名称关键词，用于模糊搜索
        page (int): 页码，从1开始
        page_size (int): 每页记录数，范围1-100

    Returns:
        general_return_json: 返回结果
            - 200: 返回委托人列表，包含分页信息
                - total: 总记录数
                - page: 当前页码
                - page_size: 每页记录数
                - total_pages: 总页数
                - data: 委托人记录列表
            - 500: 服务器错误

    Raises:
        HTTPException: 数据库操作异常
    """
    try:
        query = db.query(Consigner).filter(Consigner.is_deleted == False).order_by(Consigner.id)

        if keyword:
            query = query.filter(Consigner.consigner.like(f"%{keyword}%"))

        total = query.count()
        consigners = query.offset((page - 1) * page_size).limit(page_size).all()

        return general_return_json(
            200,
            "ok",
            {
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size,
                "data": consigners
            }
        )
    except Exception as e:
        logging.error(f"获取委托人列表出错: {str(e)}")
        return general_return_json(500, f"获取失败: {str(e)}")

@router.get("/{consigner_id}")
@required_login(roles=["admin", "salesman", "super_admin"])
async def get_consigner(
    request: Request,
    consigner_id: int,
    db: Session = Depends(get_db)
):
    """获取单个委托人详情。

    Args:
        request (Request): FastAPI 请求对象
        consigner_id (int): 委托人ID
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 返回委托人详情
            - 404: 委托人不存在
            - 500: 服务器错误

    Raises:
        HTTPException: 数据库操作异常
    """
    try:
        consigner = db.query(Consigner).filter(
            Consigner.id == consigner_id,
            Consigner.is_deleted == False
        ).first()
        
        if not consigner:
            return general_return_json(404, "委托人不存在")
            
        return general_return_json(200, "ok", consigner)
    except Exception as e:
        logging.error(f"获取委托人详情出错: {str(e)}")
        return general_return_json(500, f"获取失败: {str(e)}")

@router.post("")
@required_login(roles=["admin", "super_admin"])
async def create_consigner(
    request: Request,
    consigner: ConsignerCreate,
    db: Session = Depends(get_db)
):
    """创建新委托人。

    Args:
        request (Request): FastAPI 请求对象
        consigner (ConsignerCreate): 委托人创建数据
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 创建成功，返回新创建的委托人信息
            - 500: 创建失败

    Raises:
        HTTPException: 数据库操作异常
        ValueError: 手机号或身份证号格式验证失败
    """
    try:
        db_consigner = Consigner(
            consigner=consigner.consigner,
            id_card=consigner.id_card,
            phone=consigner.phone
        )
        db.add(db_consigner)
        db.commit()
        db.refresh(db_consigner)
        return general_return_json(200, "创建成功", db_consigner)
    except Exception as e:
        db.rollback()
        logging.error(f"创建委托人出错: {str(e)}")
        return general_return_json(500, f"创建失败: {str(e)}")

@router.put("/{consigner_id}")
@required_login(roles=["admin", "super_admin"])
async def update_consigner(
    request: Request,
    consigner_id: int,
    consigner: ConsignerUpdate,
    db: Session = Depends(get_db)
):
    """更新委托人信息。

    Args:
        request (Request): FastAPI 请求对象
        consigner_id (int): 委托人ID
        consigner (ConsignerUpdate): 委托人更新数据
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 更新成功，返回更新后的委托人信息
            - 404: 委托人不存在
            - 500: 更新失败

    Raises:
        HTTPException: 数据库操作异常
        ValueError: 手机号或身份证号格式验证失败
    """
    try:
        db_consigner = db.query(Consigner).filter(
            Consigner.id == consigner_id,
            Consigner.is_deleted == False
        ).first()
        
        if not db_consigner:
            return general_return_json(404, "委托人不存在")
        
        db_consigner.consigner = consigner.consigner
        db_consigner.id_card = consigner.id_card
        db_consigner.phone = consigner.phone
        db.commit()
        db.refresh(db_consigner)
        return general_return_json(200, "更新成功", db_consigner)
    except Exception as e:
        db.rollback()
        logging.error(f"更新委托人信息出错: {str(e)}")
        return general_return_json(500, f"更新失败: {str(e)}")

@router.delete("/{consigner_id}")
@required_login(roles=["admin", "super_admin"])
async def delete_consigner(
    request: Request,
    consigner_id: int,
    db: Session = Depends(get_db)
):
    """删除委托人（软删除）。

    Args:
        request (Request): FastAPI 请求对象
        consigner_id (int): 委托人ID
        db (Session): 数据库会话对象

    Returns:
        general_return_json: 返回结果
            - 200: 删除成功
            - 404: 委托人不存在
            - 500: 删除失败

    Raises:
        HTTPException: 数据库操作异常
    """
    try:
        db_consigner = db.query(Consigner).filter(
            Consigner.id == consigner_id,
            Consigner.is_deleted == False
        ).first()
        
        if not db_consigner:
            return general_return_json(404, "委托人不存在")
        
        # 软删除
        db_consigner.is_deleted = True
        db.commit()
        return general_return_json(200, "删除成功")
    except Exception as e:
        db.rollback()
        logging.error(f"删除委托人出错: {str(e)}")
        return general_return_json(500, f"删除失败: {str(e)}")
