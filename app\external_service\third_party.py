#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2019 Tianxiadatong, Co., Ltd. All Rights Reserved.

This module mocks an intention recognition result,
and sleep 20 seconds before return.

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06
"""

import asyncio
import base64
import copy
import json
import logging
import random
import uuid
from datetime import datetime

from exceptiongroup import catch

import config
from app.oss.client import get_base64_from_obs
import requests
from requests import Response
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.dict.schema import Prompt
from app.external_service import baidu_gateway
from app.models.models import MarketEnum, VarietyEnum, Audit, User, Message, Robot, Task, Instruction,Log

# BASE_URL = "http://10.7.111.36:12707"
BASE_URL = config.o45Config.url

async def launch_recognition_task(this_term_message_id):
    # response = requests.post("/")
    # response.raise_for_status()
    prompts = []
    content = []

    db_gen = get_db()
    db: Session = next(db_gen)

    now_message = db.query(Message).filter(Message.id == this_term_message_id).first()

    # print(f"launch_recognition_task 接收到 -- {now_message} -- {now_message.content}")

    assert now_message is not None

    related_message = db.query(Message).filter(Message.id == now_message.reply).first()


    if related_message:
        print(f"launch_recognition_task 查找相关信息 -- {related_message} -- {related_message.content}")

    if related_message is None:   # if true, we can know 'now_message' var is the true original message,contrary 'related_message' is
        content.append(now_message)
    else:
        content.append(related_message)
        for each_reply in db.query(Message).filter(Message.reply == related_message.id).order_by(Message.id).all():
            content.append(each_reply)

    robots = [r[0] for r in db.query(Robot.nickname).all()]

    for each_message in content:
        r = ''
        logging.info(each_message)
        # if each_message.type > 2:
        #     r = requests.get(each_message.content)
        #     r.raise_for_status()

        fetch_task = db.query(Task.id).filter(Task.message_id == each_message.id).first()

        built_prompt_unit = Prompt(
            content = each_message.content,
            content_type = 'Image' if each_message.type == 2 else 'Text',
            task_id=str(fetch_task[0]) if fetch_task else '',
            role='robot' if each_message.sender in robots else 'user',
            img_base64= "",
            refer=[]
        )

        # logging.info("built_prompt_unit", built_prompt_unit)
        if built_prompt_unit.content_type == 'Image':
            # logging.info("测试获取base64")
            # 测试获取base64
            status, base64_data = await get_base64_from_obs(each_message.content)
            # logging.log("-----", status, base64_data[20:0])
            if not status:
                logging.error("图片获取失败")
                return

            built_prompt_unit.img_base64 = base64_data
        else:
            built_prompt_unit.img_base64 = ''

        if  each_message.reply is not None:
            reply_message = db.query(Message).filter(Message.id == each_message.reply).first()
            if not reply_message:
                continue

            task = db.query(Task).filter(Task.message_id == reply_message.id).first()
            payload = {
                "content":reply_message.content,
                "content_type":"Image" if reply_message.type >= 2 else 'Text',
                "role":'robot' if reply_message.sender in robots else 'user',
                "task_id":task.id if task else '',
                "refer":[]
            }
            logging.info("payload", payload)
            if payload['content_type'] == 'Image':
                logging.info("测试获取base64")
                # 测试获取base64
                status, base64_data = await get_base64_from_obs(reply_message.content)
                # logging.log("-----", status, base64_data[20:0])
                if not status:
                    logging.error("图片获取失败")
                    return

                payload['img_base64'] = base64_data
            else:
                payload['img_base64'] = ''

            built_prompt_unit.refer.append(Prompt(**payload))
        prompts.append(built_prompt_unit)

    structure = build_payload(prompts)

    print(f"建造载荷")

    result = await baidu_gateway.post(structure) # 捞单

    # 临时需求，不做要素缺失的校验。
    order_data = result.get('data', {}).get('orders', [])
    if order_data:
        for i in order_data:
            i['is_order_complete'] = True

    return result

def build_payload(data:list[Prompt]):
    BasePayload = []
    for each in data:
        BasePayload.append(each.dict())
    return {
        "messages":BasePayload
    }

async def O45LocalCurrencyBusinessInstruction(audit:Audit):
    db_gen = get_db()
    db: Session = next(db_gen)
    user = db.get(User,audit.auditor_id)
    payload = {
        "guid_no":audit.guid if audit.guid else audit.instruction_id.replace("_",""), #T
        # "operator_code":"baidutest1",
        "operator_code":user.username,
        "external_order_no":audit.id,#T
        "fund_code":audit.production_id,#T
        "asset_code": "",
        "combi_code": "",
        "interbank_ins_quote_type": audit.interbank_ins_quote_type,
        "report_code":audit.security_code,#T
        "invest_type": "",
        "entrust_direction": "3" if audit.direction == 3 else "4", #T
        "face_balance":audit.denomination,
        "clear_speed": 1 if audit.liquidation_speed == 'T+1' else 0,#T
        "clear_type":"",
        "net_price":"" if audit.net_price == "NaN" or audit.net_price == "/" or audit.net_price == "" else round(float(audit.net_price), 4),
        "yield_rate": "" if audit.yield_to_maturity == "NaN" or audit.yield_to_maturity == "/" or audit.yield_to_maturity == ""  else audit.yield_to_maturity ,
        "warrant_yield_rate":"" if audit.option_yield == "NaN" or audit.option_yield == "/" or audit.option_yield == ""  or audit.option_yield == "0" else audit.option_yield ,
        "bond_interest": "",
        "interbank_rival_organ_code":audit.rival_interbank_organ_code,
        "trade_rival_code": audit.rival_code,
        "rival_trader_code":audit.rival_trader_code,
        # "direct_operator_code": "baidutest1",
        "direct_operator_code":user.username,
        "trade_operator_code":"",
        # "trade_operator_code":"v-zhaoyue",
        # "ins_begin_date":datetime.strptime(str(audit.execution), "%Y-%m-%d %H:%M:%S").strftime("%Y%m%d"),
        "ins_begin_date":datetime.strptime(str(audit.execution), "%Y-%m-%d %H:%M:%S").strftime("%Y%m%d"),
        "ins_end_date": "",
        "interbank_quote_type":"",
        "remark":audit.Remark,

    }
    try:
        a = float(payload["net_price"])
        if a == 0.0:  # 比较浮点数
            payload["net_price"] = ""  # 将 payload["net_price"] 设置为整数 0
    except ValueError:
        payload["net_price"] = ""  # 如果转换失败，将 payload["net_price"] 设置为空字符串

    # print(payload)
    response = requests.post(BASE_URL + "/ib/bond/insdir",json=payload)
    log = Log(
        operator=user.username,
        target="o45",
        action="提交o45",
        object="业务提交",
        result=False,
    )
    db.add(log)
    db.commit()
    logging.info(f"操作者:{user.username},o45提交：{payload} ,返回结果:{response.text} ")

    response.raise_for_status()
    return response

async def O45FixedIncomePlatformInstructionForShanghai(audit:Audit):
    db_gen = get_db()
    db: Session = next(db_gen)
    user = db.get(User,audit.auditor_id)

    payload = {
        "guid_no":audit.guid if audit.guid else audit.instruction_id.replace("_",""),#T
        # "operator_code":"baidutest1",
        "operator_code":user.username,
        "external_order_no":audit.id,#T
        "fund_code":audit.production_id,#T
        "asset_code":"",
        "combi_code":"",
        "market_no":1,#T
        "report_code":audit.security_code,#T
        "invest_type": "",
        "entrust_direction": audit.direction, #T
        "ins_balance":str(audit.denomination).split(".")[0].replace("w","W").replace(" W","W").replace("W","0000").replace(",",""),
        # "ins_amount": int(audit.denomination) / 100 if isinstance(audit.denomination,(int, float,str)) else int(audit.denomination) / 100 ,
        # "ins_price":audit.net_price if audit.net_price else audit.full_price,#T
        # "ins_price":round(float(audit.net_price), 3) if isinstance(audit.net_price, (int, float,str)) else "",
        "ins_amount": "" if  audit.denomination == "NaN" or audit.denomination == "缺失" else int(audit.denomination) / 100,
        "ins_price": "" if audit.net_price == "NaN" or audit.net_price == "/" or audit.net_price == "" else round(float(audit.net_price), 3),
        "engaged_no": "" if audit.agreement_number == "/" else audit.agreement_number,
        "trade_rival_code": audit.rival_code,
        "rival_trader_code":audit.rival_trader_code,
        "direct_operator_code":user.username,
        "trade_operator_code":"",
        # "trade_operator_code":"v-zhaoyue",
        # "ins_begin_date":datetime.strptime(str(audit.execution), "%Y-%m-%d %H:%M:%S").strftime("%Y%m%d"),
        "ins_begin_date":datetime.strptime(str(audit.execution), "%Y-%m-%d %H:%M:%S").strftime("%Y%m%d"),
        "ins_end_date": "",
        "mature_yield": "" if audit.yield_to_maturity == "NaN" or audit.option_yield == "/" else audit.yield_to_maturity,
        "warrant_yield_rate": "" if audit.option_yield == "NaN" or audit.option_yield == "/" else audit.option_yield,
        "clear_speed": 1 if audit.liquidation_speed == 'T+1' else 0,
        "entrust_price_type":audit.entrust_price_type,
        "remark":audit.Remark,
    }
    # print(payload)
    response = requests.post(BASE_URL + "/ufx/shfix/exsecuritis/insdir",json=payload)
    response.raise_for_status()
    log = Log(
        operator=user.username,
        target="o45",
        action="提交o45",
        object="业务提交",
        result=False,
    )
    db.add(log)
    db.commit()
    logging.info(f"操作者:{user.username},o45提交：{payload} ,返回结果:{response.text} ")
    return response

async def O45FixedIncomePlatformInstructionForShenzhen(audit:Audit):
    db_gen = get_db()
    db: Session = next(db_gen)
    user = db.get(User,audit.auditor_id)

    payload = {
        "guid_no":audit.guid if audit.guid else audit.instruction_id.replace("_",""),#T
        "operator_code":user.username,#T
        # "operator_code":"baidutest1",
        "external_order_no":audit.id,#T
        "fund_code":audit.production_id,#T
        "asset_code":"",
        "combi_code":"",
        "market_no":2,#T
        "report_code":audit.security_code,#T
        "invest_type": 1,
        "entrust_direction": "3" if audit.direction == 3 else "4",#T
        "ins_balance":str(audit.denomination).split(".")[0].replace("w","W").replace(" W","W").replace("W","0000").replace(",",""),
        # "ins_amount": int(audit.denomination) / 100 if isinstance(audit.denomination ,(int, float)) else int(audit.denomination) / 100,
        # "ins_price":round(float(audit.net_price), 4) if isinstance(audit.net_price, (int, float)) else "",#T
        "ins_amount": "" if audit.denomination == "NaN" or audit.denomination == "/" or audit.net_price == "" else int(audit.denomination) / 100,
        "ins_price": "" if audit.net_price == "NaN" or audit.net_price == "/" or audit.net_price == "" else  round(float(audit.net_price), 4), #T
        "engaged_no": "" if audit.agreement_number == "/" else audit.agreement_number,
        "rival_companycode":audit.rival_dealer_code,
        "rival_trade_party":audit.trader_entity_code,
        "trade_rival_code": audit.rival_code,
        "rival_trader_code":audit.rival_trader_code,
        "direct_operator_code":user.username,#T
        # "direct_operator_code":"baidutest1",
        "trade_operator_code":"",
        # "ins_begin_date":datetime.strptime(audit.execution, "%Y-%m-%d %H:%M:%S.%f").strftime("%Y%m%d") if audit.execution else None,
        # "ins_end_date": datetime.strptime(audit.execution, "%Y-%m-%d %H:%M:%S.%f").strftime("%Y%m%d") if audit.execution else None,
        "ins_begin_date": datetime.strptime(str(audit.execution), "%Y-%m-%d %H:%M:%S").strftime("%Y%m%d"),
        "ins_end_date": "",
        "mature_yield": "" if audit.yield_to_maturity == "NaN" or audit.option_yield == "/" else audit.yield_to_maturity,
        "warrant_yield_rate": "" if audit.option_yield == "NaN" or audit.option_yield == "/" else audit.option_yield,
        "entrust_price_type":"",
        "sz_bond_trade_method":"",
        "clear_speed": 1 if audit.liquidation_speed == 'T+1' else 0,
        "remark":audit.Remark,
    }
    # print(payload)
    response = requests.post(BASE_URL + "/ufx/szfix/exsecuritis/insdir",json=payload)
    response.raise_for_status()
    log = Log(
        operator=user.username,
        target="o45",
        action="提交o45",
        object="业务提交",
        result=False,
    )
    db.add(log)
    db.commit()
    logging.info(f"操作者:{user.username},o45提交：{payload} ,返回结果:{response.text} ")
    return response

async def O45LocalCurrencyBusinessInstructionFW(audit:Audit):
    db_gen = get_db()
    db: Session = next(db_gen)
    user = db.get(User,audit.auditor_id)

    payload = {
        "operator_code":user.username,#T
        # "operator_code":"baidutest1",
        "guid_no":audit.guid if audit.guid else audit.instruction_id.replace("_",""), #T
        "external_order_no":audit.id,
        "fund_code":audit.production_id,
        "asset_code":"",
        "combi_code":"",
        "entrust_direction": "5" if audit.direction == 5 else "6",
        "hg_days":audit.deadline,
        "ins_balance":str(audit.denomination).split(".")[0].replace("w","W").replace(" W","W").replace("W","0000").replace(",","").replace("万","0000"),
        "ins_price":audit.yield_rate.replace("%",""),
        "clear_speed": 1 if audit.liquidation_speed == 'T+1' else 0,
        "clear_type":"",
        "interbank_ins_quote_type":"",
        # "direct_operator_code":"baidutest1",
        "direct_operator_code":user.username,
        "trade_operator_code":"",
        "ins_begin_date": datetime.strptime(str(audit.execution), "%Y-%m-%d %H:%M:%S").strftime("%Y%m%d"),
        "ins_end_date": "",
        "remark":audit.Remark,
        "transfer_position_flag":"",
        "relation_ins_id":"",
        "mortgageinfo_in": [],
        "rival_trader_code":audit.rival_trader_code,
        "interbank_rival_organ_code":audit.rival_interbank_organ_code,
        # "report_code":audit.security_code,
        # "invest_type": 1,
        # "mortgage_type":"1",
        # "mortgage_ratio":audit.discount_rate,
        # "face_balance":len(audit.pledge_quantity.split("、")),
        # "mortgageinfo_in": [
        #     {
        #         "report_code": audit.pledge_coupon_code,
        #         "invest_type": "1",
        #         "face_balance": audit.pledge_quantity,
        #         "mortgage_ratio": audit.discount_rate,
        #         "mortgage_type": "1",
        #     },
        # ]

    }
    if len(audit.pledge_coupon_code.split("、")) > 0:
        num = len(audit.pledge_coupon_code.split("、"))
        discount_rate_list = []
        pledge_coupon_code_list = audit.pledge_coupon_code.split("、")
        for item in audit.discount_rate.split("、"):
            if item:
                item = float(item.replace("%", ""))
                # 此处o45传值需要转化 例如质押券折算为3%时，入参填写0.03
                item = item/100
            discount_rate_list.append(item)
        pledge_quantity_list = []
        for item in audit.pledge_quantity.split("、"):
            if item:
                item = item.replace(",", "")
            pledge_quantity_list.append(item)
        for i in range(num):
            obj = {
                "report_code": pledge_coupon_code_list[i],
                "invest_type": "1",
                "face_balance": pledge_quantity_list[i],
                "mortgage_ratio": discount_rate_list[i],
                "mortgage_type": "1",
            }
            payload["mortgageinfo_in"].append(obj)

    # print(payload)
    response = requests.post(BASE_URL + "/ib/repo/insdir",json=payload)
    response.raise_for_status()
    log = Log(
        operator=user.username,
        target="o45",
        action="提交o45",
        object="业务提交",
        result=False,
    )
    db.add(log)
    db.commit()
    logging.info(f"操作者:{user.username},o45提交：{payload} ,返回结果:{response.text} ")
    return response

async def O45FixedIncomePlatformInstructionPledge(audit: Audit) -> Response:
    db_gen = get_db()
    db: Session = next(db_gen)
    user = db.get(User,audit.auditor_id)

    payload = {
        "operator_code":user.username,#T
        # "operator_code":"baidutest1",
        # "guid_no":audit.id, #T
        "guid_no":audit.guid if audit.guid else audit.instruction_id.replace("_",""),
        "external_order_no":audit.id,#T
        "fund_code":audit.production_id, #T
        "asset_code":"",
        "combi_code":"",
        "market_no":1 if audit.market == "上海" else 2, #T
        "entrust_direction": "103" if audit.direction == 103 else "104",#T
        "hg_days": audit.deadline,#T
        "ins_balance":str(audit.denomination).split(".")[0].replace("w","W").replace(" W","W").replace("W","0000").replace(",",""),#T
        # "ins_price":audit.net_price if audit.net_price else audit.full_price,#T
        "ins_price":audit.yield_rate.replace("%", ""),
        "engaged_no": "" if audit.agreement_number == "/" else audit.agreement_number,
        "rival_seat":audit.rival_seat,
        "remark":audit.Remark,
        "comment":audit.supplementary_term,
        "direct_operator_code":user.username,#T
        # "direct_operator_code":"baidutest1",
        "trade_operator_code":"",
        "trade_rival_code": audit.rival_code,
        "rival_trade_code":audit.rival_trader_code,
        "rival_companycode":audit.rival_dealer_code,
        "rival_trade_party":audit.trader_entity_code,
        "ins_begin_date":datetime.strptime(str(audit.execution), "%Y-%m-%d %H:%M:%S").strftime("%Y%m%d") if audit.execution else None,#T
        "ins_end_date": datetime.strptime(str(audit.execution), "%Y-%m-%d %H:%M:%S").strftime("%Y%m%d") if audit.execution else None,#T
        "ins_begin_time":"",
        "ins_end_time":"",
        "transfer_position_flag":"",
        "relation_ins_id":"",
        "mortgageinfo_list":[],

        # "mortgage_type":1,#T
        # "report_code":audit.security_code,#T
        # "stock_property":"",
        # "stock_repl_type":"",
        # "invest_type":"8",#T
        # # "mortgage_ratio": int(audit.discount_rate.replace("%","")) if int(audit.discount_rate.replace("%","")) <1 else int(audit.discount_rate.replace("%",""))/100,#T
        # "mortgage_ratio": audit.discount_rate,
        # "mortgage_amount": audit.pledge_quantity,#T
    }

    type = ""
    if audit.classification == '续作':
        if audit.three_party_sequel == "是":
            type = "1"
        elif audit.three_party_sequel == "否":
            type = "2"
    else:
        type = "0"
    payload['continued_type'] = type

    if len(audit.discount_rate.split("、")) > 0:
        num = len(audit.discount_rate.split("、"))
        discount_rate_list = []
        pledge_coupon_code_list = audit.pledge_coupon_code.split("、")
        discount_rate_code_list = audit.discount_rate.split("、")
        for item in discount_rate_code_list:
            if item:
                item = item.replace("%", "")
                # if item > 1:
                #     item = item/100
            discount_rate_list.append(item)
        pledge_quantity_list = []
        for item in audit.pledge_quantity.split("、"):
            if item:
                item = item.replace(",", "")
            pledge_quantity_list.append(item)
        for i in range(num):
            obj = {
                "mortgage_type": 1,
                "report_code": pledge_coupon_code_list[i],
                "stock_property": "00",
                "stock_repl_type": "",
                "invest_type":"1",#T
                "mortgage_amount": pledge_quantity_list[i],
                "mortgage_ratio": discount_rate_list[i],
                "fund_code":audit.production_id

            }
            payload["mortgageinfo_list"].append(obj)

    # print(payload)
    response = requests.post(BASE_URL + "/ufx/negotiated/epurchase",json=payload)
    response.raise_for_status()
    log = Log(
        operator=user.username,
        target="o45",
        action="提交o45",
        object="业务提交",
        result=False,
    )
    db.add(log)
    db.commit()
    logging.info(f"操作者:{user.username},o45提交：{payload} ,返回结果:{response.text} ")
    return response


async def O45FixedIncomePlatformInstructionPledge2(audit:Audit):
    db_gen = get_db()
    db: Session = next(db_gen)
    user = db.get(User,audit.auditor_id)



    payload = {
        "operator_code":user.username,#T
        # "operator_code":"baidutest1",
        # "guid_no":audit.id, #T
        "guid_no": audit.guid if audit.guid else audit.instruction_id.replace("_",""),
        "external_order_no":audit.id,#T
        "fund_code":audit.production_id, #T
        "asset_code":"",
        "combi_code":"",
        "market_no":1 if audit.market == "上海" else 2, #T
        "entrust_direction": audit.direction,#T
        # "hg_days": audit.deadline,#T
        "hg_days":audit.deadline,
        "ins_balance":str(audit.denomination).split(".")[0].replace("w","W").replace(" W","W").replace("W","0000").replace(",",""),#T
        # "ins_price":audit.net_price if audit.net_price else audit.full_price,#T
        "ins_price": audit.yield_rate.replace("%", ""),
        "expire_settle_balance":float(audit.yield_rate.replace("%", "").replace(",",""))  * float(str(audit.denomination).replace("w","W").replace(" W","W").replace("W","0000").replace(",",""))*int(audit.deadline)/365 +float(str(audit.denomination).replace("w","W").replace(" W","W").replace("W","0000").replace(",","")) ,#T
        "engaged_no": "" if audit.agreement_number == "/" else audit.agreement_number,
        "rival_seat":audit.rival_seat,
        "original_deal_no":"",
        "contract_no":"",
        # "hg_deal_date":audit.execution,#T
        "hg_deal_date":datetime.strptime(str(audit.execution), "%Y-%m-%d %H:%M:%S").strftime("%Y%m%d"),#T
        "continued_type":"",
        "remark":audit.Remark,
        "comment":"",
        "direct_operator_code":user.username,#T
        # "direct_operator_code":"baidutest1", #T
        "trade_operator_code":"",
        "trade_rival_code": audit.rival_code,
        "rival_trade_code":audit.rival_trader_code,
        "rival_companycode":audit.rival_dealer_code,
        "rival_trade_party":audit.trader_entity_code,
        "ins_begin_date":datetime.strptime(str(audit.execution), "%Y-%m-%d %H:%M:%S").strftime("%Y%m%d"),#T
        "ins_end_date": datetime.strptime(str(audit.execution), "%Y-%m-%d %H:%M:%S").strftime("%Y%m%d"),#T
        "ins_begin_time":"",
        "ins_end_time":"",
        "transfer_position_flag":"",
        "relation_ins_id":"",
        "mortgageinfo_list":[],
        "mortgage_type":1,#T
        "report_code":audit.security_code,#T

        "stock_repl_type":"",
        # "invest_type":audit.declaration_type,#T
        "invest_type":1,
        # "mortgage_ratio": audit.discount_rate.replace("%","") if audit.discount_rate.replace("%","") <1 else int(audit.discount_rate.replace("%",""))/100,#T
        "mortgage_ratio":audit.discount_rate,
        "mortgage_amount": audit.pledge_quantity,#T
        "stock_property":"",
    }

    type = ""
    if audit.classification == '续作':
        if audit.three_party_sequel == "是":
            type = "1"
        elif audit.three_party_sequel == "否":
            type = "2"
    else:
        type = "0"
    payload['continued_type'] = type

    if len(audit.discount_rate.split("、")) > 0:
        num = len(audit.discount_rate.split("、"))
        discount_rate_list = []
        pledge_coupon_code_list = audit.pledge_coupon_code.split("、")
        discount_rate_code_list = audit.discount_rate.split("、")
        for item in discount_rate_code_list:
            if item:
                item = item.replace("%", "")
                # if item > 1:
                #     item = item/100
            discount_rate_list.append(item)
        pledge_quantity_list = []
        for item in audit.pledge_quantity.split("、"):
            if item:
                item = item.replace(",", "")
            pledge_quantity_list.append(item)
        for i in range(num):
            obj = {
                "mortgage_type": 1,
                "report_code": pledge_coupon_code_list[i],
                "stock_property": "00",
                "stock_repl_type": "",
                "invest_type":"1",#T
                "mortgage_amount": pledge_quantity_list[i],
                "mortgage_ratio": discount_rate_list[i],
                "fund_code": audit.production_id,
                "combi_code":"",

            }
            payload["mortgageinfo_list"].append(obj)


    # print(payload)
    response = requests.post(BASE_URL + "/ufx/negotiated/epurchase/later",json=payload)
    response.raise_for_status()
    log = Log(
        operator=user.username,
        target="o45",
        action="提交o45",
        object="业务提交",
        result=False,
    )
    db.add(log)
    db.commit()
    logging.info(f"操作者:{user.username},o45提交：{payload} ,返回结果:{response.text} ")
    return response

async def intention_recognition(text: str, task_id: int):
  variety = random.choice(list(VarietyEnum)).value
  market = random.choice(list(MarketEnum)).value

  b = list({"现券买卖","正逆回购","换券","初始交易","到期","续作","提前到期"})

  classes = random.choice(b)

  await asyncio.sleep(20)

  basement = {
      "task_id": task_id,
      "variety": variety,
      "market": market,
      "classification": classes,
      "execution": datetime.now().strftime("%Y-%m-%d"),
      "production": "23天下大同零食箱凤爪资产MTN002",
      "direction": random.randint(0, 1),
      "security_code": "148936.SZ ",
      "net_price": "101.1713",
      "denomination": "20050000",
      "liquidation_speed": "T+1",
      "rival": "申万宏源证券",
      "result": random.randint(0, 2)
  }

  return basement