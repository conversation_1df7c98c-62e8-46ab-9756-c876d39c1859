from enum import Enum
from pathlib import Path
from fastapi import APIRouter, HTTPException
from fastapi.responses import FileResponse

router = APIRouter(
    prefix="/template",
    tags=["template"]
)

class TemplateType(Enum):
    GROUP = "group"
    USER = "user"

TEMPLATE_FILE_MAPPING = {
    TemplateType.GROUP: "importGroupExmple.csv",
    TemplateType.USER: "importUserExample.csv"
}

@router.get("/{template_type}", summary="下载模板文件")
async def download_template(
    template_type: TemplateType
) -> FileResponse:
    """
    下载指定类型的模板文件
    
    Args:
        template_type: 模板类型（group/user）
    
    Returns:
        FileResponse: 模板文件响应
    
    Raises:
        HTTPException: 当模板文件不存在时抛出404错误
    """
    try:
        file_path = Path("app/template") / TEMPLATE_FILE_MAPPING[template_type]
        if not file_path.exists():
            raise HTTPException(
                status_code=404,
                detail=f"模板文件 {TEMPLATE_FILE_MAPPING[template_type]} 不存在"
            )
        
        
        return FileResponse(
            path=file_path,
            filename=TEMPLATE_FILE_MAPPING[template_type],
            media_type="text/csv"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail="下载模板文件失败"
        ) 