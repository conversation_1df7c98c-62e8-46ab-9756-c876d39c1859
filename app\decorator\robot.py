#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2019 Tianxiadatong, Co., Ltd. All Rights Reserved.

This module defines a decorator that that validates authentication in session
before allowing access to an API endpoint.

It is now deprecated and kept for archival or compatibility purposes.

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06
"""

from functools import wraps

from fastapi import Request, HTTPException



expected_token = "114514"
#此装饰器验证接收群聊机器人接收信息接口是否被非 BOT 方请求，约定：在请求头携带  authorization : Bearer {expected_token}
def verify_bot():
    def decorator(func):
        @wraps(func)
        async def wrapper(request: Request, *args, **kwargs):
            auth: str = request.headers.get("Authorization")
            if not auth or not auth.startswith("Bearer "):
                raise HTTPException(status_code=401, detail="Identification not acknowledged")

            token = auth.split(" ")[1]
            if token != expected_token:
                raise HTTPException(status_code=403, detail="Identification not acknowledged")

            return await func(request, *args, **kwargs)

        return wrapper

    return decorator