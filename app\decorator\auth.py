#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2019 Tianxiadatong, Co., Ltd. All Rights Reserved.

This module defines a decorator that validates authentication in session
before allowing access to an API endpoint.

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06
"""

from typing import Callable, Optional, List
from fastapi import Request, HTTPException
from functools import wraps


kicked_user_ids = set()

#踢人下线方法。
#修改权限后把被修改者一脚踢下线，此人ID会被存在 set() 内，直到登录成功时再进行懒删除，用户最多十几个，此内存代价可以接受
def kick_user(user_id:int):
    global kicked_user_ids
    kicked_user_ids.add(user_id)

#在fast API route上使用此注解，方法参数必须携带 FastAPI.Request 类，
#功能： @required_login() 此方法需要登录才能使用，否则权限不足拒绝访问
#      @required_login( role = ["admin",....] ) 需要对应权限才能使用，否则不给访问
#
#权限： super_admin , root , admin , salesman ，roles[]参数使用4个之中的零个或多个，填这4个以外的str会导致方法任何人永远权限不足
#
def required_login(roles: Optional[List[str]]=None):
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            request: Optional[Request] = None

            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            if request is None:
                request = kwargs.get("request")

            if not request or not isinstance(request, Request):
                print("no request")
                raise HTTPException(status_code=403, detail="Identity Not Acknowledged")

            if not request.session.get("auth"):
                print("no auth")
                raise HTTPException(status_code=403, detail="Identity Not Acknowledged")



            if request.session.get("auth").get("id") in kicked_user_ids:
                request.session.clear()
                raise HTTPException(status_code=403, detail="请重新登录")


            if roles:
                user_role = request.session.get("auth").get("role")
                if user_role not in roles:
                    raise HTTPException(status_code=403, detail="权限不足")
            return await func(*args, **kwargs)
        return wrapper
    return decorator